import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddHasBotInstaToPackages1960352900008
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add has_bot_insta column to packages table
    await queryRunner.query(`
      ALTER TABLE "packages"
      ADD COLUMN "has_bot_insta" BOOLEAN NOT NULL DEFAULT false
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove the column if migration is reverted
    await queryRunner.query(`
      ALTER TABLE "packages"
      DROP COLUMN "has_bot_insta"
    `);
  }
}
