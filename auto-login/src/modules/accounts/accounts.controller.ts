import {
  Controller,
  Post,
  Get,
  Patch,
  Delete,
  Param,
  Query,
  Body,
  HttpException,
  HttpStatus,
  UseGuards,
  ParseIntPipe,
  UnauthorizedException,
  NotFoundException,
  Req,
  DefaultValuePipe,
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { Roles } from '../auth/roles.decorator';
import { AccountsService } from './accounts.service';
import { CreateAccountDto } from './dto/create-account.dto';
import { UpdateAccountDto } from './dto/update-account.dto';
import { RolesGuard } from '../auth/roles.guard';

@Controller('accounts')
@UseGuards(JwtAuthGuard)
export class AccountsController {
  constructor(private readonly accountsService: AccountsService) {}

  @Get('cookies')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async getAllCookies(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('search') search?: string,
    @Query('accountId') accountId?: string,
    @Query('serverId') serverId?: string,
  ) {
    try {
      // Parse accountId and serverId if provided
      const parsedAccountId = accountId ? parseInt(accountId) : undefined;
      const parsedServerId = serverId ? parseInt(serverId) : undefined;

      const result = await this.accountsService.findAllCookies(
        page,
        limit,
        search,
        parsedAccountId,
        parsedServerId,
      );
      return result;
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve cookies: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post()
  @UseGuards(RolesGuard)
  @Roles('admin')
  async create(@Body() createAccountDto: CreateAccountDto) {
    try {
      if (!createAccountDto) {
        throw new HttpException(
          'No account data provided',
          HttpStatus.BAD_REQUEST,
        );
      }

      if (!createAccountDto.website_url) {
        throw new HttpException(
          'Website URL is required',
          HttpStatus.BAD_REQUEST,
        );
      }

      // img_intro is now directly passed as Base64 in the DTO
      return await this.accountsService.create(createAccountDto);
    } catch (error) {
      throw new HttpException(
        'Failed to create account: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  async findAll(
    @Query('page', new ParseIntPipe({ optional: true })) page = 1,
    @Query('limit', new ParseIntPipe({ optional: true })) limit = 50,
    @Query('search') search?: string,
    @Query('filter') filter?: string,
    @Query('userId', new ParseIntPipe({ optional: true })) userId?: number,
    @Query('sortBy') sortBy?: string,
    @Query('sortOrder') sortOrder?: 'ASC' | 'DESC',
  ) {
    try {
      return await this.accountsService.findAll(
        page,
        limit,
        search,
        filter,
        userId,
        sortBy,
        sortOrder,
      );
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve accounts',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('my')
  async findMyAccounts(
    @Req() req,
    @Query('page', new ParseIntPipe({ optional: true })) page = 1,
    @Query('limit', new ParseIntPipe({ optional: true })) limit = 10,
    @Query('search') search?: string,
    @Query('sortBy') sortBy?: string,
    @Query('sortOrder') sortOrder?: 'ASC' | 'DESC',
  ) {
    try {
      const userId = req.user.sub;
      return await this.accountsService.findAll(
        page,
        limit,
        search,
        undefined,
        userId,
        sortBy,
        sortOrder,
      );
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve your accounts',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('my-with-products')
  async findMyAccountsWithProducts(
    @Req() req,
    @Query('page', new ParseIntPipe({ optional: true })) page = 1,
    @Query('limit', new ParseIntPipe({ optional: true })) limit = 100,
    @Query('search') search?: string,
    @Query('sortBy') sortBy?: string,
    @Query('sortOrder') sortOrder?: 'ASC' | 'DESC',
  ) {
    try {
      const userId = req.user.sub;
      return await this.accountsService.findAll(
        page,
        limit,
        search,
        undefined,
        userId,
        sortBy,
        sortOrder,
      );
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve your accounts with products',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number) {
    try {
      return await this.accountsService.findOne(id);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }
      throw new HttpException(
        'Failed to retrieve account',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Patch(':id')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateAccountDto: UpdateAccountDto,
  ) {
    try {
      if (!updateAccountDto) {
        throw new HttpException(
          'No account data provided',
          HttpStatus.BAD_REQUEST,
        );
      }

      if (!updateAccountDto.website_url) {
        throw new HttpException(
          'Website URL is required',
          HttpStatus.BAD_REQUEST,
        );
      }

      // img_intro is now directly passed as Base64 in the DTO
      return await this.accountsService.update(id, updateAccountDto);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }
      throw new HttpException(
        'Failed to update account: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete(':id')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async remove(@Param('id', ParseIntPipe) id: number) {
    try {
      await this.accountsService.remove(id);
      return { message: 'Account deleted successfully' };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }
      throw new HttpException(
        'Failed to delete account',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post(':id/cookie')
  async saveCookie(
    @Param('id', ParseIntPipe) id: number,
    @Body('cookie_data') cookieData: string,
    @Body('server_id', new DefaultValuePipe(1), ParseIntPipe) serverId: number,
    @Req() req,
  ) {
    try {
      if (!cookieData) {
        throw new HttpException(
          'Cookie data is required',
          HttpStatus.BAD_REQUEST,
        );
      }

      // Format cookie data from string to JSON array format
      let formattedCookieData = cookieData;
      try {
        // Check if the cookie data is already in JSON format
        JSON.parse(cookieData);
      } catch (e) {
        // If not in JSON format, convert it to JSON array format
        formattedCookieData = this.formatCookieString(cookieData);
      }

      const userId = req.user.sub;
      const account = await this.accountsService.saveCookie(
        id,
        formattedCookieData,
        userId,
        serverId,
      );
      return { message: 'Cookie saved successfully', account };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }
      throw new HttpException(
        'Failed to save cookie: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // Helper method to format cookie string to JSON array format
  private formatCookieString(cookieStr: string): string {
    try {
      // Split the cookie string by tabs or spaces
      const lines = cookieStr.split(/\n/).filter((line) => line.trim());
      const cookies = [];

      for (const line of lines) {
        const parts = line.split(/\t|\s+/).filter((part) => part.trim());
        if (parts.length >= 3) {
          // At minimum, we need name, value, domain
          const cookie = {
            name: parts[0],
            value: parts[1],
            domain: parts[2],
            path: parts.length > 3 ? parts[3] : '/',
            expires:
              parts.length > 4
                ? new Date(parts[4]).getTime() / 1000
                : undefined,
            size: parts[0].length + parts[1].length,
            httpOnly: false,
            secure: true,
            session: false,
            priority: 'Medium',
            sameParty: false,
            sourceScheme: 'Secure',
          };
          cookies.push(cookie);
        }
      }

      return JSON.stringify(cookies);
    } catch (error) {
      console.error('Error formatting cookie string:', error);
      // Return the original string if formatting fails
      return cookieStr;
    }
  }

  @Get(':id/cookie')
  async getCookie(
    @Param('id', ParseIntPipe) id: number,
    @Query('server_id', new DefaultValuePipe(1), ParseIntPipe) serverId: number,
    @Req() req,
  ) {
    try {
      const userId = req.user.sub;
      const cookieData = await this.accountsService.getCookie(
        id,
        userId,
        serverId,
      );

      if (!cookieData) {
        throw new HttpException(
          'No cookie found or you do not have access to this account',
          HttpStatus.NOT_FOUND,
        );
      }

      return cookieData;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }
      throw new HttpException(
        'Failed to get cookie: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id/server-cookies')
  async getServerCookies(@Param('id', ParseIntPipe) id: number, @Req() req) {
    try {
      const userId = req.user.sub;
      const serverCookies = await this.accountsService.getAllServerCookies(
        id,
        userId,
      );
      return { serverCookies };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }
      throw new HttpException(
        'Failed to get server cookies: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id/cookies/:serverId')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async getServerCookie(
    @Param('id', ParseIntPipe) id: number,
    @Param('serverId', ParseIntPipe) serverId: number,
    @Req() req,
  ) {
    try {
      const userId = req.user.sub;
      const cookieData = await this.accountsService.getCookie(
        id,
        userId,
        serverId,
      );

      if (!cookieData) {
        throw new HttpException(
          'No cookie found or you do not have access to this account',
          HttpStatus.NOT_FOUND,
        );
      }

      return cookieData;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }
      throw new HttpException(
        'Failed to get cookie: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete(':id/cookies/:serverId')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async deleteServerCookie(
    @Param('id', ParseIntPipe) id: number,
    @Param('serverId', ParseIntPipe) serverId: number,
    @Req() req,
  ) {
    try {
      const userId = req.user.sub;
      const success = await this.accountsService.deleteServerCookie(
        id,
        serverId,
        userId,
      );
      if (success) {
        return { message: 'Server cookie deleted successfully' };
      } else {
        throw new HttpException(
          'Server cookie not found',
          HttpStatus.NOT_FOUND,
        );
      }
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }
      throw new HttpException(
        'Failed to delete server cookie: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post(':id/login')
  async autoLogin(@Param('id', ParseIntPipe) id: string) {
    try {
      const session = await this.accountsService.autoLogin(id);
      return session;
    } catch (error) {
      if (error.message === 'Account not found') {
        throw new HttpException('Account not found', HttpStatus.NOT_FOUND);
      }
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new HttpException(
        'Failed to perform auto login',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
