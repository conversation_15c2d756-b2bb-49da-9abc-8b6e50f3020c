import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGenerated<PERSON><PERSON>umn,
  CreateDate<PERSON>olumn,
  UpdateDateColumn,
  ManyToMany,
  JoinTable,
  OneToMany,
} from 'typeorm';
import { Package } from '../../packages/entities/package.entity';
import { ServerCookie } from './server-cookie.entity';
import { ProfileShare } from '../../profiles/entities/profile-share.entity';
import { ProfileAccessLog } from '../../profiles/entities/profile-access-log.entity';

@Entity('accounts')
export class Account {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  website_url: string;

  @Column({ nullable: true })
  username: string;

  @Column({ nullable: true })
  password: string;

  @Column({ default: 1 })
  login_cookie: number;

  // NEW: Link to browser profile
  @Column({ nullable: true })
  browser_profile_id: number;

  // NEW: Profile synchronization status
  @Column({ default: 'not_synced' })
  sync_status: string; // not_synced, syncing, synced, error

  @Column({ nullable: true })
  name: string;

  @Column({ nullable: true, type: 'text' })
  img_intro: string;

  @Column({ nullable: true, type: 'text' })
  description: string;

  @Column({ nullable: true, type: 'text' })
  cookie_data: string;

  @Column({ type: 'timestamp', nullable: true })
  last_login: Date;

  // NEW: Last sync timestamp
  @Column({ type: 'timestamp', nullable: true })
  last_sync: Date;

  @Column({ type: 'integer', default: 0 })
  sort: number;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @ManyToMany(() => Package)
  @JoinTable({
    name: 'account_packages',
    joinColumn: { name: 'account_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'package_id', referencedColumnName: 'id' },
  })
  packages: Package[];

  @OneToMany(() => ServerCookie, (serverCookie) => serverCookie.account)
  serverCookies: ServerCookie[];

  // NEW: Profile sharing relationships
  @OneToMany(() => ProfileShare, (profileShare) => profileShare.account)
  profileShares: ProfileShare[];

  @OneToMany(
    () => ProfileAccessLog,
    (profileAccessLog) => profileAccessLog.account,
  )
  accessLogs: ProfileAccessLog[];
}
