export default () => ({
  database: {
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT, 10) || 5432,
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_DATABASE,
  },
  jwt: {
    secret: process.env.JWT_SECRET,
    expiresIn: process.env.JWT_EXPIRATION || '1h',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRATION || '7d',
  },
  proxy: {
    host: process.env.PROXY_HOST,
    port: parseInt(process.env.PROXY_PORT, 10),
    username: process.env.PROXY_USERNAME,
    password: process.env.PROXY_PASSWORD,
  },
  security: {
    cors: {
      origins: process.env.CORS_ORIGINS
        ? process.env.CORS_ORIGINS.split(',')
        : ['http://localhost:3001'],
      methods: process.env.CORS_METHODS
        ? process.env.CORS_METHODS.split(',')
        : ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    },
    rateLimit: {
      ttl: parseInt(process.env.RATE_LIMIT_TTL, 10) || 60, // seconds
      limit: parseInt(process.env.RATE_LIMIT_MAX, 10) || 100, // max requests per TTL
    },
  },
  google: {
    clientId: process.env.GOOGLE_CLIENT_ID,
    clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    callbackURL:
      process.env.GOOGLE_CALLBACK_URL ||
      'http://localhost:3000/auth/google/callback',
    tokenCallbackURL:
      process.env.GOOGLE_TOKEN_CALLBACK_URL ||
      'http://localhost:3000/auth/token/google/callback',
  },
  discord: {
    clientId: process.env.DISCORD_CLIENT_ID,
    clientSecret: process.env.DISCORD_CLIENT_SECRET,
    callbackURL:
      process.env.DISCORD_CALLBACK_URL ||
      'http://localhost:3000/auth/discord/callback',
    guildId: process.env.DISCORD_GUILD_ID,
    botToken: process.env.DISCORD_BOT_TOKEN,
    inviteCode: process.env.DISCORD_INVITE_CODE,
    memberRoleId: process.env.DISCORD_MEMBER_ROLE_ID,
    inviteCallbackUrl:
      process.env.DISCORD_INVITE_CALLBACK_URL ||
      'http://localhost:3000/discord/invite/{inviteCode}/complete',
    generalChatChannelId: process.env.DISCORD_GENERAL_CHAT_CHANNEL_ID,
    vipBuyerRoleId: process.env.DISCORD_VIP_BUYER_ROLE_ID,
    designBuyerRoleId: process.env.DISCORD_DESIGN_BUYER_ROLE_ID,
    marketingBuyerRoleId: process.env.DISCORD_MARKETING_BUYER_ROLE_ID,
  },
  mail: {
    host: process.env.MAIL_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.MAIL_PORT, 10) || 587,
    secure: process.env.MAIL_SECURE === 'true',
    user: process.env.MAIL_USER,
    password: process.env.MAIL_PASSWORD,
    from: process.env.MAIL_FROM || '<EMAIL>',
  },
  APP_URL: process.env.APP_URL || 'http://localhost:3001',
  ADMIN_APP_URL: process.env.ADMIN_APP_URL || 'http://localhost:3000',
  paypal: {
    clientId: process.env.PAYPAL_CLIENT_ID,
    clientSecret: process.env.PAYPAL_CLIENT_SECRET,
  },
});
