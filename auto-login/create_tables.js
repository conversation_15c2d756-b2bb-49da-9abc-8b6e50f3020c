const { Client } = require('pg');

const client = new Client({
  host: 'localhost',
  port: 5432,
  user: 'postgres',
  password: 'postgres',
  database: 'auto_login',
});

const createTables = async () => {
  try {
    await client.connect();
    console.log('Connected to database');

    // Create profile_groups table
    await client.query(`
      CREATE TABLE IF NOT EXISTS profile_groups (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) UNIQUE NOT NULL,
        description TEXT,
        created_by_admin_id INTEGER NOT NULL,
        status VARCHAR(50) DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (created_by_admin_id) REFERENCES users(id)
      );
    `);
    console.log('✅ Created profile_groups table');

    // Create profiles table
    await client.query(`
      CREATE TABLE IF NOT EXISTS profiles (
        id SERIAL PRIMARY KEY,
        name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
        account_id INTEGER NOT NULL,
        profile_group_id INTEGER NOT NULL,
        created_by_admin_id INTEGER NOT NULL,
        status VARCHAR(50) DEFAULT 'active',
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (account_id) REFERENCES accounts(id),
        FOREIGN KEY (profile_group_id) REFERENCES profile_groups(id),
        FOREIGN KEY (created_by_admin_id) REFERENCES users(id)
      );
    `);
    console.log('✅ Created profiles table');

    // Create user_profile_group_access table
    await client.query(`
      CREATE TABLE IF NOT EXISTS user_profile_group_access (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL,
        profile_group_id INTEGER NOT NULL,
        granted_by_admin_id INTEGER NOT NULL,
        status VARCHAR(50) DEFAULT 'active',
        permissions JSONB,
        expires_at TIMESTAMP,
        granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id),
        FOREIGN KEY (profile_group_id) REFERENCES profile_groups(id),
        FOREIGN KEY (granted_by_admin_id) REFERENCES users(id)
      );
    `);
    console.log('✅ Created user_profile_group_access table');

    // Create profile_shares table
    await client.query(`
      CREATE TABLE IF NOT EXISTS profile_shares (
        id SERIAL PRIMARY KEY,
        account_id INTEGER NOT NULL,
        shared_with_user_id INTEGER NOT NULL,
        shared_by_admin_id INTEGER NOT NULL,
        status VARCHAR(50) DEFAULT 'active',
        expires_at TIMESTAMP,
        permissions JSONB,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (account_id) REFERENCES accounts(id),
        FOREIGN KEY (shared_with_user_id) REFERENCES users(id),
        FOREIGN KEY (shared_by_admin_id) REFERENCES users(id)
      );
    `);
    console.log('✅ Created profile_shares table');

    // Create profile_access_logs table
    await client.query(`
      CREATE TABLE IF NOT EXISTS profile_access_logs (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL,
        account_id INTEGER NOT NULL,
        action VARCHAR(255) NOT NULL,
        metadata JSONB,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id),
        FOREIGN KEY (account_id) REFERENCES accounts(id)
      );
    `);
    console.log('✅ Created profile_access_logs table');

    // Create indexes
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_profile_groups_created_by_admin ON profile_groups(created_by_admin_id);
      CREATE INDEX IF NOT EXISTS idx_profiles_account_id ON profiles(account_id);
      CREATE INDEX IF NOT EXISTS idx_profiles_profile_group_id ON profiles(profile_group_id);
      CREATE INDEX IF NOT EXISTS idx_profiles_created_by_admin ON profiles(created_by_admin_id);
      CREATE INDEX IF NOT EXISTS idx_user_profile_group_access_user_id ON user_profile_group_access(user_id);
      CREATE INDEX IF NOT EXISTS idx_user_profile_group_access_profile_group_id ON user_profile_group_access(profile_group_id);
      CREATE INDEX IF NOT EXISTS idx_profile_shares_account_id ON profile_shares(account_id);
      CREATE INDEX IF NOT EXISTS idx_profile_shares_shared_with_user_id ON profile_shares(shared_with_user_id);
      CREATE INDEX IF NOT EXISTS idx_profile_access_logs_user_id ON profile_access_logs(user_id);
      CREATE INDEX IF NOT EXISTS idx_profile_access_logs_account_id ON profile_access_logs(account_id);
    `);
    console.log('✅ Created indexes');

    // Create updated_at trigger function
    await client.query(`
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
          NEW.updated_at = CURRENT_TIMESTAMP;
          RETURN NEW;
      END;
      $$ language 'plpgsql';
    `);

    // Create triggers
    await client.query(`
      DROP TRIGGER IF EXISTS update_profile_groups_updated_at ON profile_groups;
      CREATE TRIGGER update_profile_groups_updated_at BEFORE UPDATE ON profile_groups FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
      
      DROP TRIGGER IF EXISTS update_profiles_updated_at ON profiles;
      CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
      
      DROP TRIGGER IF EXISTS update_profile_shares_updated_at ON profile_shares;
      CREATE TRIGGER update_profile_shares_updated_at BEFORE UPDATE ON profile_shares FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    `);
    console.log('✅ Created triggers');

    console.log('🎉 All profile management tables created successfully!');

  } catch (error) {
    console.error('❌ Error creating tables:', error);
  } finally {
    await client.end();
  }
};

createTables();
