# Antidetect Browser Application

This project consists of multiple components for managing antidetect browser profiles and automation.

## 🆕 New Features - Profile Management System

### ✨ What's New
- **Admin Profile Management**: Complete profile organization system
- **Hierarchical Structure**: Profile Groups → Profiles → User Access
- **Browser Integration**: Direct Camoufox browser launching
- **Permission System**: Granular user access control
- **Modern UI**: Ant Design-based interface

### 🎯 Key Features
- ✅ **Profile Groups**: Organize profiles by teams or purposes
- ✅ **Profile Management**: Link accounts to groups for browser launching  
- ✅ **User Assignments**: Grant users access with configurable permissions
- ✅ **Browser Launch**: One-click Camoufox browser launching
- ✅ **Admin Controls**: Full CRUD operations for all entities
- ✅ **Security**: Role-based access control and JWT authentication

## 🏗️ Project Structure

```
antidetect-browser/
├── frontend/                 # React frontend application
│   ├── src/
│   │   ├── components/
│   │   │   ├── ProfileManagement/  # NEW: Profile management components
│   │   │   ├── Login.js            # UPDATED: Email/password login
│   │   │   └── ModernSidebar.js    # UPDATED: Admin menu
│   │   ├── pages/
│   │   │   └── ManageProfiles.js   # NEW: Admin profile management page
│   │   └── contexts/
│   │       └── AuthContext.js      # Authentication context
│   └── package.json
├── auto-login/              # NestJS backend API
│   ├── src/
│   │   ├── modules/
│   │   │   ├── profiles/           # NEW: Profile management module
│   │   │   ├── auth/               # UPDATED: Email/password auth
│   │   │   ├── users/              # UPDATED: User management
│   │   │   └── accounts/           # UPDATED: Account management
│   │   └── main.ts
│   └── package.json
├── camoufox/                # Camoufox browser profiles
├── scripts/                 # Setup and utility scripts
│   └── run-tests.sh         # NEW: Test runner script
└── docs/                    # Documentation
    └── PROFILE_MANAGEMENT_GUIDE.md  # NEW: User guide
```

## 🚀 Quick Start

### Prerequisites
- Node.js 16+ and npm
- PostgreSQL database
- Camoufox browser installed

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd antidetect-browser
```

2. **Setup Backend**
```bash
cd auto-login
npm install
cp .env.example .env  # Configure your database and JWT settings
npm run start:dev
```

3. **Setup Frontend**
```bash
cd ../frontend
npm install
npm start
```

4. **Access the Application**
- Frontend: http://localhost:3000
- Backend API: http://localhost:3000/api

### First Time Setup

1. **Login as Admin**
   - Use Google OAuth or email/password
   - Ensure your user has `role: 'admin'`

2. **Access Profile Management**
   - Click "Manage Profile" in the sidebar (admin only)
   - Start creating profile groups and profiles

## 📊 Profile Management Workflow

### 1. Create Profile Groups
```
Admin → Manage Profile → Profile Groups → Create Profile Group
```
- Organize profiles by teams (Marketing, Sales, etc.)
- Add descriptions for clarity

### 2. Create Profiles
```
Admin → Manage Profile → Profiles → Create Profile
```
- Link existing accounts to profile groups
- Profiles enable browser launching

### 3. Assign User Access
```
Admin → Manage Profile → User Assignments → Assign User to Group
```
- Grant users access to specific profile groups
- Configure permissions (launch browser, view profiles, export data)
- Set access levels (read/write/full)

### 4. Launch Browsers
```
User → Profile List → Launch Browser Button
```
- One-click Camoufox browser launching
- Uses account credentials and profile settings

## 🔧 API Endpoints

### Authentication
- `POST /auth/login` - Email/password login
- `POST /auth/google` - Google OAuth login
- `GET /auth/profile` - Get current user

### Profile Management (Admin Only)
- `GET /profiles/groups` - List profile groups
- `POST /profiles/groups` - Create profile group
- `GET /profiles/items` - List profiles
- `POST /profiles/items` - Create profile
- `GET /profiles/group-access` - List user assignments
- `POST /profiles/group-access` - Create user assignment

### Browser Integration
- `POST /api/profiles/:accountId/launch-browser` - Launch browser

## 🧪 Testing

### Run All Tests
```bash
./scripts/run-tests.sh
```

### Individual Test Suites
```bash
# Backend tests
cd auto-login && npm run test

# Frontend tests
cd frontend && npm test

# Integration tests
cd frontend && npm test -- --testPathPattern=integration
```

## 🔒 Security Features

- **JWT Authentication**: Secure token-based auth
- **Role-Based Access**: Admin/user role separation
- **Input Validation**: DTO validation with class-validator
- **SQL Injection Protection**: TypeORM query builder
- **XSS Prevention**: Input sanitization

## 📚 Documentation

- [Profile Management User Guide](docs/PROFILE_MANAGEMENT_GUIDE.md)
- [API Documentation](auto-login/README.md)
- [Frontend Components](frontend/src/components/README.md)

## 🛠️ Development

### Database Migrations
```bash
cd auto-login
npm run migration:generate -- -n ProfileManagement
npm run migration:run
```

### Adding New Features
1. Backend: Add entities, services, controllers in `auto-login/src/modules/`
2. Frontend: Add components in `frontend/src/components/`
3. Tests: Add tests in `__tests__/` directories
4. Documentation: Update relevant docs

## 🚨 Troubleshooting

### Common Issues

**Backend won't start**
- Check database connection in `.env`
- Ensure PostgreSQL is running
- Run migrations: `npm run migration:run`

**Frontend build errors**
- Clear node_modules: `rm -rf node_modules && npm install`
- Check for missing dependencies

**Profile Management not visible**
- Ensure user has `role: 'admin'` in database
- Check JWT token validity

**Browser launch fails**
- Verify Camoufox installation
- Check account exists and is valid
- Ensure profile has account_id

## 📈 Performance Tips

- Limit profiles per group (recommended: <100)
- Regular cleanup of expired user access
- Monitor browser processes
- Use pagination for large datasets

## 🤝 Contributing

1. Fork the repository
2. Create feature branch: `git checkout -b feature/new-feature`
3. Commit changes: `git commit -am 'Add new feature'`
4. Push to branch: `git push origin feature/new-feature`
5. Submit pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

*For detailed usage instructions, see [Profile Management Guide](docs/PROFILE_MANAGEMENT_GUIDE.md)*
