#!/bin/bash

# Instagram Scraping Database Migration Script
# This script activates the virtual environment and runs the migration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"
BACKEND_DIR="$PROJECT_DIR/backend"

print_status "Instagram Scraping Database Migration"
print_status "======================================"

# Check if backend directory exists
if [ ! -d "$BACKEND_DIR" ]; then
    print_error "Backend directory not found: $BACKEND_DIR"
    exit 1
fi

# Check if virtual environment exists
if [ ! -d "$BACKEND_DIR/venv" ]; then
    print_error "Virtual environment not found: $BACKEND_DIR/venv"
    print_status "Please run setup first:"
    print_status "  ./scripts/setup-local.sh"
    exit 1
fi

# Check if migration script exists
if [ ! -f "$BACKEND_DIR/simple_instagram_migration.py" ]; then
    print_error "Migration script not found: $BACKEND_DIR/simple_instagram_migration.py"
    exit 1
fi

print_status "Changing to backend directory: $BACKEND_DIR"
cd "$BACKEND_DIR"

print_status "Running simple Instagram scraping migration..."
print_status "This migration uses direct SQLite and doesn't require virtual environment"
python3 simple_instagram_migration.py

# Check exit code
if [ $? -eq 0 ]; then
    print_success "Migration completed successfully!"
    print_status ""
    print_status "Next steps:"
    print_status "1. Start all services: ./scripts/start-all-services.sh"
    print_status "2. Open the app and go to Instagram > Scraping"
    print_status "3. Create Instagram profiles and start scraping"
else
    print_error "Migration failed!"
    exit 1
fi
