#!/bin/bash

# Start all services for Facebook Automation Desktop Application
# Includes: Auto-login service (NestJS), Backend (FastAPI), and Frontend (React/Electron)

set -e

echo "🚀 Starting All Services - Facebook Automation Desktop Application"
echo "=================================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
AUTO_LOGIN_PORT=3000
BACKEND_PORT=8000
FRONTEND_PORT=3001
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"

# PIDs for cleanup
AUTO_LOGIN_PID=""
BACKEND_PID=""
FRONTEND_PID=""

# Cleanup function
cleanup() {
    print_status "Shutting down all services..."
    
    if [ -n "$AUTO_LOGIN_PID" ]; then
        kill $AUTO_LOGIN_PID 2>/dev/null || true
        print_status "Auto-login service stopped"
    fi
    
    if [ -n "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
        print_status "Backend stopped"
    fi
    
    if [ -n "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
        print_status "Frontend stopped"
    fi
    
    # Kill any remaining processes
    pkill -f "npm run start:dev" 2>/dev/null || true
    pkill -f "uvicorn main:app" 2>/dev/null || true
    pkill -f "npm run dev" 2>/dev/null || true
    pkill -f "electron" 2>/dev/null || true
    
    print_success "All services stopped"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Check if ports are available
check_ports() {
    print_status "Checking port availability..."
    
    if lsof -Pi :$AUTO_LOGIN_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_error "Port $AUTO_LOGIN_PORT is already in use (Auto-login service)"
        exit 1
    fi
    
    if lsof -Pi :$BACKEND_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_error "Port $BACKEND_PORT is already in use (Backend)"
        exit 1
    fi
    
    if lsof -Pi :$FRONTEND_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_warning "Port $FRONTEND_PORT is already in use (Frontend will try next available port)"
    fi
    
    print_success "Ports are available"
}

# Start auto-login service (NestJS)
start_auto_login() {
    print_status "Starting auto-login service (NestJS)..."
    
    cd "$PROJECT_DIR/auto-login"
    
    # Check if node_modules exists
    if [ ! -d "node_modules" ]; then
        print_error "Auto-login dependencies not found"
        print_status "Please run: cd auto-login && npm install"
        exit 1
    fi
    
    # Start auto-login service
    print_status "Starting NestJS server on port $AUTO_LOGIN_PORT..."
    npm run start:dev &
    AUTO_LOGIN_PID=$!
    
    # Wait for service to start
    sleep 5
    
    # Check if auto-login started successfully
    if kill -0 $AUTO_LOGIN_PID 2>/dev/null; then
        print_success "Auto-login service started (PID: $AUTO_LOGIN_PID)"
        print_status "Auto-login API: http://localhost:$AUTO_LOGIN_PORT"
    else
        print_error "Failed to start auto-login service"
        exit 1
    fi
}

# Start backend server (FastAPI)
start_backend() {
    print_status "Starting backend server (FastAPI)..."
    
    cd "$PROJECT_DIR/backend"
    
    # Check if virtual environment exists
    if [ ! -d "venv" ]; then
        print_error "Backend virtual environment not found"
        print_status "Please run setup first: ./scripts/setup-local.sh"
        exit 1
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Start backend with uvicorn
    print_status "Starting FastAPI server on port $BACKEND_PORT..."
    uvicorn main:app --reload --host 0.0.0.0 --port $BACKEND_PORT &
    BACKEND_PID=$!
    
    # Wait for server to start
    sleep 3
    
    # Check if backend started successfully
    if kill -0 $BACKEND_PID 2>/dev/null; then
        print_success "Backend server started (PID: $BACKEND_PID)"
        print_status "Backend API: http://localhost:$BACKEND_PORT"
        print_status "API Docs: http://localhost:$BACKEND_PORT/docs"
    else
        print_error "Failed to start backend server"
        exit 1
    fi
}

# Start frontend server (React/Electron)
start_frontend() {
    print_status "Starting frontend server (React/Electron)..."
    
    cd "$PROJECT_DIR/frontend"
    
    # Check if node_modules exists
    if [ ! -d "node_modules" ]; then
        print_error "Frontend dependencies not found"
        print_status "Please run: cd frontend && npm install"
        exit 1
    fi
    
    # Set environment variables
    export REACT_APP_API_URL="http://localhost:$BACKEND_PORT"
    export REACT_APP_WS_URL="ws://localhost:$BACKEND_PORT"
    
    # Start frontend development server
    print_status "Starting React/Electron development server..."
    npm run dev &
    FRONTEND_PID=$!
    
    # Wait for server to start
    sleep 5
    
    # Check if frontend started successfully
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        print_success "Frontend server started (PID: $FRONTEND_PID)"
        print_status "Electron app should open automatically"
    else
        print_error "Failed to start frontend server"
        exit 1
    fi
}

# Show running services status
show_status() {
    print_status "Service Status:"
    echo ""
    
    if [ -n "$AUTO_LOGIN_PID" ] && kill -0 $AUTO_LOGIN_PID 2>/dev/null; then
        print_success "✅ Auto-login (NestJS): Running (PID: $AUTO_LOGIN_PID) - http://localhost:$AUTO_LOGIN_PORT"
    else
        print_error "❌ Auto-login (NestJS): Not running"
    fi
    
    if [ -n "$BACKEND_PID" ] && kill -0 $BACKEND_PID 2>/dev/null; then
        print_success "✅ Backend (FastAPI): Running (PID: $BACKEND_PID) - http://localhost:$BACKEND_PORT"
    else
        print_error "❌ Backend (FastAPI): Not running"
    fi
    
    if [ -n "$FRONTEND_PID" ] && kill -0 $FRONTEND_PID 2>/dev/null; then
        print_success "✅ Frontend (React/Electron): Running (PID: $FRONTEND_PID) - Electron App"
    else
        print_error "❌ Frontend (React/Electron): Not running"
    fi
    
    echo ""
}

# Monitor services
monitor_services() {
    print_status "Monitoring services... (Press Ctrl+C to stop all)"
    echo ""
    
    while true; do
        # Check if auto-login is still running
        if [ -n "$AUTO_LOGIN_PID" ] && ! kill -0 $AUTO_LOGIN_PID 2>/dev/null; then
            print_error "Auto-login service died unexpectedly"
            cleanup
        fi
        
        # Check if backend is still running
        if [ -n "$BACKEND_PID" ] && ! kill -0 $BACKEND_PID 2>/dev/null; then
            print_error "Backend process died unexpectedly"
            cleanup
        fi
        
        # Check if frontend is still running
        if [ -n "$FRONTEND_PID" ] && ! kill -0 $FRONTEND_PID 2>/dev/null; then
            print_error "Frontend process died unexpectedly"
            cleanup
        fi
        
        sleep 5
    done
}

# Main function
main() {
    print_status "Starting Facebook Automation Desktop Application..."
    print_status "Project directory: $PROJECT_DIR"
    
    # Check ports
    check_ports
    
    # Start services in order
    start_auto_login
    start_backend
    start_frontend
    
    # Show status
    sleep 2
    show_status
    
    print_success "🎉 All services started successfully!"
    echo ""
    print_status "Available endpoints:"
    echo "  🔐 Auto-login API: http://localhost:$AUTO_LOGIN_PORT"
    echo "  📡 Backend API: http://localhost:$BACKEND_PORT"
    echo "  📚 API Documentation: http://localhost:$BACKEND_PORT/docs"
    echo "  🖥️  Desktop App: Electron window should open"
    echo ""
    print_status "Login flow:"
    echo "  1. Use Google login in the Electron app"
    echo "  2. Auto-login service handles OAuth"
    echo "  3. Backend verifies authentication"
    echo "  4. Frontend receives user data"
    echo ""
    
    # Monitor services
    monitor_services
}

# Run main function
main "$@"
