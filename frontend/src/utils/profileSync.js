/**
 * Profile Sync Service
 * Handles synchronization between NestJS and FastAPI databases
 */

import { message } from 'antd';
import { apiService } from '../services/api';
import { logger, LogUtils, LOG_CATEGORIES } from './logger';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, A<PERSON><PERSON>rror, ERROR_TYPES, ERROR_SEVERITY } from './errorHandler';

/**
 * Profile Sync Manager
 */
export class ProfileSyncManager {
  constructor() {
    this.syncInProgress = false;
  }

  /**
   * Sync a single profile from NestJS to FastAPI
   */
  async syncProfileToFastAPI(profile) {
    const timer = LogUtils.logUserAction('sync_profile_to_fastapi', { profileId: profile.id });
    
    try {
      logger.info(LOG_CATEGORIES.PROFILE, `Starting profile sync to FastAPI for ID: ${profile.id}`, {
        profileName: profile.name,
        accountId: profile.account_id
      });

      // Prepare profile data for FastAPI
      const profileData = {
        id: profile.id,
        name: profile.name,
        account_id: profile.account_id || profile.id, // Use profile.id as fallback
        status: profile.status || 'active',
        created_at: profile.created_at || new Date().toISOString(),
        // Add any other required fields
        user_id: 1, // Admin user ID
        group_id: null,
        proxy_id: null,
        browser_config: {},
        notes: `Synced from NestJS - ${profile.name}`
      };

      logger.debug(LOG_CATEGORIES.PROFILE, `Prepared profile data for sync: ${profile.id}`, profileData);

      // Create profile in FastAPI database
      const apiTimer = LogUtils.logApiCall('POST', '/api/profiles', profileData);
      const response = await apiService.post('/api/profiles', profileData);
      apiTimer.end();

      if (response.data && (response.data.success !== false)) {
        logger.info(LOG_CATEGORIES.PROFILE, `Profile synced successfully to FastAPI: ${profile.id}`, {
          response: response.data
        });
        
        timer.end();
        return {
          success: true,
          message: `Profile ${profile.id} synced successfully`,
          data: response.data
        };
      } else {
        const errorMsg = response.data?.message || 'Unknown sync error';
        logger.error(LOG_CATEGORIES.PROFILE, `Profile sync failed for ID: ${profile.id}`, {
          error: errorMsg,
          response: response.data
        });
        
        timer.end();
        return {
          success: false,
          message: errorMsg,
          data: response.data
        };
      }

    } catch (error) {
      logger.error(LOG_CATEGORIES.PROFILE, `Profile sync error for ID: ${profile.id}`, {
        error: error.message,
        stack: error.stack
      });

      // Check if profile already exists (409 conflict)
      if (error.response?.status === 409) {
        logger.info(LOG_CATEGORIES.PROFILE, `Profile ${profile.id} already exists in FastAPI - sync not needed`);
        timer.end();
        return {
          success: true,
          message: `Profile ${profile.id} already exists in FastAPI`,
          data: null
        };
      }

      timer.end();
      return {
        success: false,
        message: error.message,
        error
      };
    }
  }

  /**
   * Auto-sync profile before capture
   */
  async autoSyncBeforeCapture(profile) {
    const timer = LogUtils.logUserAction('auto_sync_before_capture', { profileId: profile.id });
    
    try {
      logger.info(LOG_CATEGORIES.PROFILE, `Auto-sync check before capture for profile: ${profile.id}`);

      // First check if profile exists in FastAPI
      try {
        const checkTimer = LogUtils.logApiCall('GET', `/api/profiles/${profile.id}`);
        const checkResponse = await apiService.get(`/api/profiles/${profile.id}`);
        checkTimer.end();

        if (checkResponse.data) {
          logger.debug(LOG_CATEGORIES.PROFILE, `Profile ${profile.id} already exists in FastAPI - no sync needed`);
          timer.end();
          return {
            success: true,
            message: 'Profile already synced',
            synced: false
          };
        }
      } catch (checkError) {
        if (checkError.response?.status === 404) {
          logger.info(LOG_CATEGORIES.PROFILE, `Profile ${profile.id} not found in FastAPI - proceeding with sync`);
        } else {
          logger.warn(LOG_CATEGORIES.PROFILE, `Error checking profile existence in FastAPI: ${checkError.message}`);
        }
      }

      // Profile doesn't exist in FastAPI, sync it
      logger.info(LOG_CATEGORIES.PROFILE, `Syncing profile ${profile.id} to FastAPI before capture`);
      message.info(`🔄 Syncing profile ${profile.id} to capture backend...`);

      const syncResult = await this.syncProfileToFastAPI(profile);

      if (syncResult.success) {
        logger.info(LOG_CATEGORIES.PROFILE, `Auto-sync successful for profile: ${profile.id}`);
        message.success(`✅ Profile ${profile.id} synced successfully!`);
        
        timer.end();
        return {
          success: true,
          message: 'Profile synced successfully',
          synced: true
        };
      } else {
        logger.error(LOG_CATEGORIES.PROFILE, `Auto-sync failed for profile: ${profile.id}`, {
          error: syncResult.message
        });
        message.error(`❌ Failed to sync profile ${profile.id}: ${syncResult.message}`);
        
        timer.end();
        return syncResult;
      }

    } catch (error) {
      logger.error(LOG_CATEGORIES.PROFILE, `Auto-sync error for profile: ${profile.id}`, {
        error: error.message,
        stack: error.stack
      });

      timer.end();
      return {
        success: false,
        message: error.message,
        error
      };
    }
  }

  /**
   * Bulk sync all profiles from NestJS to FastAPI
   */
  async bulkSyncProfiles() {
    if (this.syncInProgress) {
      message.warning('Profile sync is already in progress');
      return;
    }

    this.syncInProgress = true;
    const timer = LogUtils.logUserAction('bulk_sync_profiles');

    try {
      logger.info(LOG_CATEGORIES.SYSTEM, 'Starting bulk profile sync from NestJS to FastAPI');
      message.info('🔄 Starting bulk profile sync...');

      // Get all profiles from NestJS
      const nestjsTimer = LogUtils.logApiCall('GET', 'http://localhost:3000/profiles/items');
      const nestjsProfiles = await apiService.getProfileItems();
      nestjsTimer.end();

      const profiles = nestjsProfiles?.data || [];
      logger.info(LOG_CATEGORIES.SYSTEM, `Found ${profiles.length} profiles in NestJS to sync`);

      if (profiles.length === 0) {
        message.info('No profiles found to sync');
        timer.end();
        return { success: true, message: 'No profiles to sync', synced: 0 };
      }

      // Sync each profile
      const results = [];
      let successCount = 0;
      let failureCount = 0;

      for (const profile of profiles) {
        try {
          const syncResult = await this.syncProfileToFastAPI(profile);
          results.push({ profile: profile.id, result: syncResult });
          
          if (syncResult.success) {
            successCount++;
          } else {
            failureCount++;
          }

          // Small delay between syncs
          await new Promise(resolve => setTimeout(resolve, 100));
        } catch (error) {
          logger.error(LOG_CATEGORIES.PROFILE, `Bulk sync error for profile ${profile.id}`, {
            error: error.message
          });
          results.push({ profile: profile.id, result: { success: false, message: error.message } });
          failureCount++;
        }
      }

      const summary = {
        total: profiles.length,
        success: successCount,
        failed: failureCount,
        results
      };

      logger.info(LOG_CATEGORIES.SYSTEM, 'Bulk profile sync completed', summary);

      if (failureCount === 0) {
        message.success(`✅ All ${successCount} profiles synced successfully!`);
      } else {
        message.warning(`⚠️ Sync completed: ${successCount} success, ${failureCount} failed`);
      }

      timer.end();
      return { success: true, message: 'Bulk sync completed', ...summary };

    } catch (error) {
      logger.error(LOG_CATEGORIES.SYSTEM, 'Bulk profile sync failed', {
        error: error.message,
        stack: error.stack
      });

      message.error(`❌ Bulk sync failed: ${error.message}`);
      timer.end();
      return { success: false, message: error.message, error };
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * Check sync status between backends
   */
  async checkSyncStatus() {
    const timer = LogUtils.logUserAction('check_sync_status');

    try {
      logger.info(LOG_CATEGORIES.SYSTEM, 'Checking sync status between backends');

      // Get profiles from both backends
      const [nestjsProfiles, fastApiProfiles] = await Promise.all([
        apiService.getProfileItems().then(r => r?.data || []),
        apiService.get('/api/profiles').then(r => r?.data || [])
      ]);

      const nestjsIds = new Set(nestjsProfiles.map(p => p.id));
      const fastApiIds = new Set(fastApiProfiles.map(p => p.id));

      const onlyInNestjs = [...nestjsIds].filter(id => !fastApiIds.has(id));
      const onlyInFastApi = [...fastApiIds].filter(id => !nestjsIds.has(id));
      const inBoth = [...nestjsIds].filter(id => fastApiIds.has(id));

      const syncStatus = {
        nestjsCount: nestjsIds.size,
        fastApiCount: fastApiIds.size,
        syncedCount: inBoth.length,
        onlyInNestjs,
        onlyInFastApi,
        inBoth,
        needsSync: onlyInNestjs.length > 0
      };

      logger.info(LOG_CATEGORIES.SYSTEM, 'Sync status check completed', syncStatus);

      timer.end();
      return syncStatus;

    } catch (error) {
      logger.error(LOG_CATEGORIES.SYSTEM, 'Sync status check failed', {
        error: error.message
      });

      timer.end();
      throw error;
    }
  }
}

/**
 * Global profile sync manager instance
 */
export const profileSyncManager = new ProfileSyncManager();

/**
 * Utility functions
 */
export const ProfileSyncUtils = {
  /**
   * Quick sync for a profile before capture
   */
  syncBeforeCapture: async (profile) => {
    return await profileSyncManager.autoSyncBeforeCapture(profile);
  },

  /**
   * Manual sync for a profile
   */
  syncProfile: async (profile) => {
    return await profileSyncManager.syncProfileToFastAPI(profile);
  },

  /**
   * Bulk sync all profiles
   */
  bulkSync: async () => {
    return await profileSyncManager.bulkSyncProfiles();
  },

  /**
   * Check sync status
   */
  checkStatus: async () => {
    return await profileSyncManager.checkSyncStatus();
  }
};

// Make profile sync available globally in development
if (process.env.NODE_ENV === 'development') {
  window.ProfileSync = {
    profileSyncManager,
    ProfileSyncUtils,
    syncProfile: ProfileSyncUtils.syncProfile,
    bulkSync: ProfileSyncUtils.bulkSync,
    checkStatus: ProfileSyncUtils.checkStatus
  };
}
