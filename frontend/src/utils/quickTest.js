/**
 * Quick Test Script
 * Simple verification that all systems are working
 */

import { message } from 'antd';
import { logger, LOG_CATEGORIES } from './logger';

/**
 * Quick verification test
 */
export const runQuickVerification = () => {
  try {
    logger.info(LOG_CATEGORIES.SYSTEM, 'Running quick verification test');
    
    // Test 1: Logger is working
    console.log('✅ Logger system is working');
    
    // Test 2: Utilities are imported correctly
    if (typeof window.AppLogger !== 'undefined') {
      console.log('✅ AppLogger is available globally');
    }
    
    if (typeof window.AppTesting !== 'undefined') {
      console.log('✅ AppTesting is available globally');
    }
    
    if (typeof window.AdminTestFlow !== 'undefined') {
      console.log('✅ AdminTestFlow is available globally');
    }

    if (typeof window.InterceptorTest !== 'undefined') {
      console.log('✅ InterceptorTest is available globally');
    }

    // Test 3: Message system is working
    message.success('✅ Quick verification completed successfully!');
    
    logger.info(LOG_CATEGORIES.SYSTEM, 'Quick verification test completed successfully');
    
    return {
      success: true,
      message: 'All systems operational',
      timestamp: new Date().toISOString()
    };
    
  } catch (error) {
    logger.error(LOG_CATEGORIES.SYSTEM, 'Quick verification test failed', {
      error: error.message
    });
    
    message.error('❌ Quick verification failed');
    
    return {
      success: false,
      message: error.message,
      timestamp: new Date().toISOString()
    };
  }
};

// Make quick test available globally
if (process.env.NODE_ENV === 'development') {
  window.runQuickTest = runQuickVerification;

  // Make unified profile manager test available
  window.testUnifiedProfile = async () => {
    try {
      const { ProfileAPI } = await import('./unifiedProfileManager');

      // Get available profiles
      const availableProfiles = await ProfileAPI.getAvailableProfiles();
      console.log('Available profiles:', availableProfiles);

      if (availableProfiles.length === 0) {
        return { success: false, error: 'No profiles available' };
      }

      // Test with first available profile
      const testProfile = {
        id: availableProfiles[0],
        name: `Test_Profile_${availableProfiles[0]}`,
        account_id: availableProfiles[0],
        status: 'active'
      };

      console.log('Testing with profile:', testProfile);

      // Test capture and save
      const result = await ProfileAPI.captureAndSave(testProfile, { showLoadingMessage: true });

      return result;
    } catch (error) {
      console.error('Unified profile test failed:', error);
      return { success: false, error: error.message };
    }
  };

  // Also keep the old test for compatibility
  window.testWorkingProfile = window.testUnifiedProfile;

  // Make profile sync available
  window.syncProfiles = async () => {
    try {
      const { ProfileSyncUtils } = await import('./profileSync');
      return await ProfileSyncUtils.bulkSync();
    } catch (error) {
      console.error('Profile sync failed:', error);
      return { success: false, error: error.message };
    }
  };

  window.checkSyncStatus = async () => {
    try {
      const { ProfileSyncUtils } = await import('./profileSync');
      return await ProfileSyncUtils.checkStatus();
    } catch (error) {
      console.error('Sync status check failed:', error);
      return { success: false, error: error.message };
    }
  };

  // Make interceptor test available
  window.testInterceptor = async () => {
    try {
      const { runInterceptorTests } = await import('./interceptorTest');
      return await runInterceptorTests();
    } catch (error) {
      console.error('Interceptor test failed:', error);
      return { success: false, error: error.message };
    }
  };
}

// Auto-run quick test when loaded
setTimeout(() => {
  runQuickVerification();
}, 1000);
