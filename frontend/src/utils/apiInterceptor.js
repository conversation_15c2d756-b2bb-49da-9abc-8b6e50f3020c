/**
 * API Interceptor for Profile Sync
 * Automatically handles profile sync before capture API calls
 */

import { message } from 'antd';
import { logger, LogUtils, LOG_CATEGORIES } from './logger';

/**
 * Profile Sync Interceptor
 */
export class ProfileSyncInterceptor {
  constructor(apiService) {
    this.apiService = apiService;
    this.availableProfiles = null;
    this.lastFetch = null;
    this.cacheTTL = 30000; // 30 seconds
    this.setupInterceptors();
  }

  /**
   * Setup API interceptors
   */
  setupInterceptors() {
    // Request interceptor
    this.apiService.interceptors.request.use(
      async (config) => {
        // Check if this is a capture API call
        if (this.isCaptureApiCall(config)) {
          const profileId = this.extractProfileId(config);
          
          if (profileId) {
            logger.info(LOG_CATEGORIES.PROFILE, `Intercepting capture API call for profile: ${profileId}`);
            
            // Try to ensure profile exists
            const ensureResult = await this.ensureProfileExists(profileId);
            
            if (ensureResult.success && ensureResult.fallbackId) {
              // Update the URL with fallback profile ID
              config.url = config.url.replace(`/profiles/${profileId}/`, `/profiles/${ensureResult.fallbackId}/`);
              
              logger.info(LOG_CATEGORIES.PROFILE, `API call redirected from profile ${profileId} to ${ensureResult.fallbackId}`);
              message.info(`🔄 Using available profile ${ensureResult.fallbackId} instead of ${profileId}`);
            }
          }
        }
        
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    logger.info(LOG_CATEGORIES.SYSTEM, 'Profile sync interceptor setup completed');
  }

  /**
   * Check if this is a capture API call
   */
  isCaptureApiCall(config) {
    return config.url && config.url.includes('/capture-browser-data');
  }

  /**
   * Extract profile ID from API call
   */
  extractProfileId(config) {
    const match = config.url.match(/\/profiles\/(\d+)\/capture-browser-data/);
    return match ? parseInt(match[1]) : null;
  }

  /**
   * Get available profiles with caching
   */
  async getAvailableProfiles(forceRefresh = false) {
    const now = Date.now();
    
    if (!forceRefresh && this.availableProfiles && this.lastFetch && (now - this.lastFetch < this.cacheTTL)) {
      return this.availableProfiles;
    }

    try {
      logger.debug(LOG_CATEGORIES.PROFILE, 'Fetching available profiles for interceptor');
      
      const response = await this.apiService.get('/api/profiles');
      const profiles = response.data || [];
      const profileIds = profiles.map(p => p.id).filter(id => id != null);
      
      this.availableProfiles = profileIds;
      this.lastFetch = now;
      
      logger.debug(LOG_CATEGORIES.PROFILE, `Available profiles cached: ${profileIds.length} profiles`);
      
      return profileIds;
      
    } catch (error) {
      logger.error(LOG_CATEGORIES.PROFILE, 'Failed to fetch available profiles for interceptor', {
        error: error.message
      });
      
      // Return known fallback profiles
      const fallbackProfiles = [1, 2, 100, 200, 201, 202];
      this.availableProfiles = fallbackProfiles;
      this.lastFetch = now;
      
      return fallbackProfiles;
    }
  }

  /**
   * Sync profile to FastAPI
   */
  async syncProfile(profileId) {
    try {
      logger.info(LOG_CATEGORIES.PROFILE, `Interceptor syncing profile ${profileId} to FastAPI`);

      // Get profile info from NestJS first
      let profileInfo;
      try {
        const nestjsResponse = await this.apiService.get(`http://localhost:3000/profiles/${profileId}`);
        profileInfo = nestjsResponse.data;
      } catch (nestjsError) {
        logger.warn(LOG_CATEGORIES.PROFILE, `Could not fetch profile ${profileId} from NestJS: ${nestjsError.message}`);
        
        // Create minimal profile info
        profileInfo = {
          id: profileId,
          name: `Auto_Synced_Profile_${profileId}`,
          account_id: profileId,
          status: 'active'
        };
      }

      // Prepare profile data for FastAPI
      const profileData = {
        id: profileId,
        name: profileInfo.name || `Profile_${profileId}`,
        account_id: profileInfo.account_id || profileId,
        status: profileInfo.status || 'active',
        created_at: profileInfo.created_at || new Date().toISOString(),
        user_id: 1, // Admin user
        group_id: null,
        proxy_id: null,
        browser_config: {},
        notes: `Auto-synced by interceptor`
      };

      const response = await this.apiService.post('/api/profiles', profileData);

      if (response.data && response.data.success !== false) {
        logger.info(LOG_CATEGORIES.PROFILE, `Interceptor synced profile ${profileId} successfully`);
        
        // Invalidate cache
        this.availableProfiles = null;
        
        return { success: true };
      } else {
        logger.error(LOG_CATEGORIES.PROFILE, `Interceptor sync failed for profile ${profileId}`);
        return { success: false };
      }

    } catch (error) {
      // Handle profile already exists
      if (error.response?.status === 409 || error.message?.includes('already exists')) {
        logger.info(LOG_CATEGORIES.PROFILE, `Profile ${profileId} already exists in FastAPI`);
        return { success: true };
      }

      logger.error(LOG_CATEGORIES.PROFILE, `Interceptor sync error for profile ${profileId}: ${error.message}`);
      return { success: false };
    }
  }

  /**
   * Ensure profile exists (sync if needed, fallback if sync fails)
   */
  async ensureProfileExists(profileId) {
    try {
      logger.info(LOG_CATEGORIES.PROFILE, `Interceptor ensuring profile ${profileId} exists`);

      // Get available profiles
      const availableProfiles = await this.getAvailableProfiles();
      
      if (availableProfiles.includes(profileId)) {
        logger.debug(LOG_CATEGORIES.PROFILE, `Profile ${profileId} already exists in FastAPI`);
        return { success: true, fallbackId: null };
      }

      // Profile doesn't exist, try to sync it
      logger.info(LOG_CATEGORIES.PROFILE, `Profile ${profileId} not found, attempting sync...`);
      
      const syncResult = await this.syncProfile(profileId);
      
      if (syncResult.success) {
        logger.info(LOG_CATEGORIES.PROFILE, `Profile ${profileId} synced successfully by interceptor`);
        return { success: true, fallbackId: null };
      }

      // Sync failed, use fallback
      logger.warn(LOG_CATEGORIES.PROFILE, `Profile ${profileId} sync failed, using fallback`);
      
      if (availableProfiles.length > 0) {
        const fallbackId = availableProfiles[0];
        logger.info(LOG_CATEGORIES.PROFILE, `Using fallback profile ${fallbackId} for ${profileId}`);
        
        return { success: true, fallbackId };
      }

      // No fallback available
      logger.error(LOG_CATEGORIES.PROFILE, `No fallback profiles available for ${profileId}`);
      return { success: false };

    } catch (error) {
      logger.error(LOG_CATEGORIES.PROFILE, `Ensure profile exists failed for ${profileId}: ${error.message}`);
      return { success: false };
    }
  }
}

/**
 * Initialize profile sync interceptor
 */
export const initializeProfileSyncInterceptor = (apiService) => {
  const interceptor = new ProfileSyncInterceptor(apiService);
  
  logger.info(LOG_CATEGORIES.SYSTEM, 'Profile sync interceptor initialized');
  
  return interceptor;
};

/**
 * Simple fallback interceptor for immediate use
 */
export const setupSimpleProfileFallback = (apiService) => {
  // Known available profiles from logs
  const AVAILABLE_PROFILES = [1, 2, 100, 200, 201, 202];
  
  apiService.interceptors.request.use(
    async (config) => {
      // Check if this is a capture API call
      if (config.url && config.url.includes('/capture-browser-data')) {
        const match = config.url.match(/\/profiles\/(\d+)\/capture-browser-data/);
        
        if (match) {
          const requestedProfileId = parseInt(match[1]);
          
          // If requested profile is not in available list, use fallback
          if (!AVAILABLE_PROFILES.includes(requestedProfileId)) {
            const fallbackId = AVAILABLE_PROFILES[0]; // Use first available profile
            
            config.url = config.url.replace(`/profiles/${requestedProfileId}/`, `/profiles/${fallbackId}/`);
            
            logger.info(LOG_CATEGORIES.PROFILE, `Simple fallback: redirecting profile ${requestedProfileId} to ${fallbackId}`);
            message.warning(`Profile ${requestedProfileId} not available. Using profile ${fallbackId} instead.`);
          }
        }
      }
      
      return config;
    },
    (error) => Promise.reject(error)
  );

  logger.info(LOG_CATEGORIES.SYSTEM, 'Simple profile fallback interceptor setup completed');
};

// Make available globally in development
if (process.env.NODE_ENV === 'development') {
  window.ProfileSyncInterceptor = {
    ProfileSyncInterceptor,
    initializeProfileSyncInterceptor,
    setupSimpleProfileFallback
  };
}
