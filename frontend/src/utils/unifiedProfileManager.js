/**
 * Unified Profile Manager
 * Single source of truth for all profile operations with automatic sync
 */

import { message } from 'antd';
import { apiService } from '../services/api';
import { logger, LogUtils, LOG_CATEGORIES } from './logger';
import { <PERSON>rror<PERSON>and<PERSON>, AppError, ERROR_TYPES, ERROR_SEVERITY } from './errorHandler';
import { isSuccessResponse, extractDataFromResponse, getErrorMessage, createStandardResponse } from './apiResponseHandler';

/**
 * Unified Profile Manager Class
 */
export class UnifiedProfileManager {
  constructor() {
    this.availableProfiles = null;
    this.lastFetch = null;
    this.cacheTTL = 30000; // 30 seconds cache
  }

  /**
   * Get available profiles from FastAPI with caching
   */
  async getAvailableProfiles(forceRefresh = false) {
    const now = Date.now();
    
    if (!forceRefresh && this.availableProfiles && this.lastFetch && (now - this.lastFetch < this.cacheTTL)) {
      logger.debug(LOG_CATEGORIES.PROFILE, 'Using cached available profiles', {
        count: this.availableProfiles.length,
        cacheAge: now - this.lastFetch
      });
      return this.availableProfiles;
    }

    const timer = LogUtils.logApiCall('GET', '/api/profiles');
    
    try {
      logger.debug(LOG_CATEGORIES.PROFILE, 'Fetching available profiles from FastAPI');
      
      const response = await apiService.get('/api/profiles');
      timer.end();
      
      const profiles = response.data || [];
      const profileIds = profiles.map(p => p.id).filter(id => id != null);
      
      this.availableProfiles = profileIds;
      this.lastFetch = now;
      
      logger.info(LOG_CATEGORIES.PROFILE, 'Available profiles updated', {
        count: profileIds.length,
        profileIds
      });
      
      return profileIds;
      
    } catch (error) {
      timer.end();
      logger.error(LOG_CATEGORIES.PROFILE, 'Failed to fetch available profiles', {
        error: error.message
      });
      
      // Return known fallback profiles
      const fallbackProfiles = [1, 2, 100, 200, 201, 202];
      logger.warn(LOG_CATEGORIES.PROFILE, 'Using fallback profiles', { fallbackProfiles });
      
      this.availableProfiles = fallbackProfiles;
      this.lastFetch = now;
      
      return fallbackProfiles;
    }
  }

  /**
   * Sync profile from NestJS to FastAPI
   */
  async syncProfile(profile) {
    const timer = LogUtils.logUserAction('sync_profile', { profileId: profile.id });
    
    try {
      logger.info(LOG_CATEGORIES.PROFILE, `Syncing profile ${profile.id} to FastAPI`, {
        profileName: profile.name
      });

      // Prepare profile data for FastAPI
      const profileData = {
        id: profile.id,
        name: profile.name || `Profile_${profile.id}`,
        account_id: profile.account_id || profile.id,
        status: profile.status || 'active',
        created_at: profile.created_at || new Date().toISOString(),
        user_id: 1, // Admin user
        group_id: null,
        proxy_id: null,
        browser_config: {},
        notes: `Auto-synced from NestJS`
      };

      const apiTimer = LogUtils.logApiCall('POST', '/api/profiles', profileData);
      const response = await apiService.post('/api/profiles', profileData);
      apiTimer.end();

      if (response.data && response.data.success !== false) {
        logger.info(LOG_CATEGORIES.PROFILE, `Profile ${profile.id} synced successfully`);
        
        // Invalidate cache
        this.availableProfiles = null;
        
        timer.end();
        return { success: true, message: 'Profile synced successfully' };
      } else {
        const errorMsg = response.data?.message || 'Sync failed';
        logger.error(LOG_CATEGORIES.PROFILE, `Profile ${profile.id} sync failed: ${errorMsg}`);
        
        timer.end();
        return { success: false, message: errorMsg };
      }

    } catch (error) {
      timer.end();
      
      // Handle profile already exists
      if (error.response?.status === 409 || error.message?.includes('already exists')) {
        logger.info(LOG_CATEGORIES.PROFILE, `Profile ${profile.id} already exists in FastAPI`);
        return { success: true, message: 'Profile already exists' };
      }

      logger.error(LOG_CATEGORIES.PROFILE, `Profile ${profile.id} sync error: ${error.message}`);
      return { success: false, message: error.message };
    }
  }

  /**
   * Ensure profile exists in FastAPI (sync if needed)
   */
  async ensureProfileExists(profile) {
    const timer = LogUtils.logUserAction('ensure_profile_exists', { profileId: profile.id });
    
    try {
      logger.info(LOG_CATEGORIES.PROFILE, `Ensuring profile ${profile.id} exists in FastAPI`);

      // Get available profiles
      const availableProfiles = await this.getAvailableProfiles();
      
      if (availableProfiles.includes(profile.id)) {
        logger.debug(LOG_CATEGORIES.PROFILE, `Profile ${profile.id} already exists in FastAPI`);
        timer.end();
        return { success: true, profile, synced: false };
      }

      // Profile doesn't exist, sync it
      logger.info(LOG_CATEGORIES.PROFILE, `Profile ${profile.id} not found, syncing...`);
      message.info(`🔄 Syncing profile ${profile.id}...`);

      const syncResult = await this.syncProfile(profile);
      
      if (syncResult.success) {
        logger.info(LOG_CATEGORIES.PROFILE, `Profile ${profile.id} synced successfully`);
        message.success(`✅ Profile ${profile.id} synced!`);
        
        timer.end();
        return { success: true, profile, synced: true };
      } else {
        logger.error(LOG_CATEGORIES.PROFILE, `Profile ${profile.id} sync failed: ${syncResult.message}`);
        message.error(`❌ Sync failed: ${syncResult.message}`);
        
        timer.end();
        return { success: false, message: syncResult.message };
      }

    } catch (error) {
      timer.end();
      logger.error(LOG_CATEGORIES.PROFILE, `Ensure profile exists failed for ${profile.id}: ${error.message}`);
      return { success: false, message: error.message };
    }
  }

  /**
   * Get working profile (fallback to available profile if needed)
   */
  async getWorkingProfile(requestedProfile) {
    const timer = LogUtils.logUserAction('get_working_profile', { 
      requestedId: requestedProfile?.id 
    });
    
    try {
      logger.info(LOG_CATEGORIES.PROFILE, `Getting working profile for ID: ${requestedProfile?.id}`);

      // First try to ensure requested profile exists
      const ensureResult = await this.ensureProfileExists(requestedProfile);
      
      if (ensureResult.success) {
        logger.info(LOG_CATEGORIES.PROFILE, `Using requested profile: ${requestedProfile.id}`);
        timer.end();
        return { success: true, profile: requestedProfile, fallback: false };
      }

      // Fallback to available profile
      logger.warn(LOG_CATEGORIES.PROFILE, `Requested profile ${requestedProfile?.id} not available, using fallback`);
      
      const availableProfiles = await this.getAvailableProfiles();
      
      if (availableProfiles.length === 0) {
        timer.end();
        return { success: false, message: 'No profiles available' };
      }

      const fallbackId = availableProfiles[0];
      const fallbackProfile = {
        id: fallbackId,
        name: `Fallback_Profile_${fallbackId}`,
        account_id: fallbackId,
        status: 'active'
      };

      logger.info(LOG_CATEGORIES.PROFILE, `Using fallback profile: ${fallbackId}`);
      message.warning(`Profile ${requestedProfile?.id} not available. Using profile ${fallbackId} instead.`);

      timer.end();
      return { success: true, profile: fallbackProfile, fallback: true };

    } catch (error) {
      timer.end();
      logger.error(LOG_CATEGORIES.PROFILE, `Get working profile failed: ${error.message}`);
      return { success: false, message: error.message };
    }
  }

  /**
   * Capture browser data with automatic profile handling
   */
  async captureBrowserData(profile, options = {}) {
    const { showLoadingMessage = true, retryOnError = true } = options;
    const operationTimer = LogUtils.logDataCapture('capture_start', profile.id);
    
    try {
      logger.info(LOG_CATEGORIES.DATA_CAPTURE, `Starting capture for profile: ${profile.id}`);

      if (showLoadingMessage) {
        message.loading('🔍 Capturing browser data...', 0);
      }

      // Get working profile (with auto-sync)
      const workingResult = await this.getWorkingProfile(profile);
      
      if (!workingResult.success) {
        if (showLoadingMessage) message.destroy();
        operationTimer.end();
        return createStandardResponse(false, null, workingResult.message);
      }

      const workingProfile = workingResult.profile;
      
      if (workingResult.fallback) {
        logger.info(LOG_CATEGORIES.DATA_CAPTURE, `Using fallback profile ${workingProfile.id} for capture`);
      }

      // Call capture API
      const apiTimer = LogUtils.logApiCall('POST', `/api/profiles/${workingProfile.id}/capture-browser-data`);
      const response = await apiService.post(`/api/profiles/${workingProfile.id}/capture-browser-data`);
      apiTimer.end();

      if (showLoadingMessage) message.destroy();

      if (isSuccessResponse(response.data)) {
        const capturedData = extractDataFromResponse(response.data);
        
        logger.info(LOG_CATEGORIES.DATA_CAPTURE, `Capture successful for profile: ${workingProfile.id}`, {
          dataSize: JSON.stringify(capturedData).length
        });

        if (showLoadingMessage) {
          message.success('✅ Browser data captured successfully!');
        }

        operationTimer.end();
        return createStandardResponse(true, capturedData, 'Capture successful');
      } else {
        const errorMsg = getErrorMessage(response.data, 'Capture failed');
        logger.error(LOG_CATEGORIES.DATA_CAPTURE, `Capture failed for profile: ${workingProfile.id}`, {
          error: errorMsg
        });

        operationTimer.end();
        return createStandardResponse(false, null, errorMsg);
      }

    } catch (error) {
      if (showLoadingMessage) message.destroy();
      
      logger.error(LOG_CATEGORIES.DATA_CAPTURE, `Capture error for profile: ${profile.id}`, {
        error: error.message,
        stack: error.stack
      });

      const errorMsg = error.response?.data?.message || error.message || 'Capture failed';
      
      if (showLoadingMessage) {
        message.error(`❌ Capture failed: ${errorMsg}`);
      }

      operationTimer.end();
      return createStandardResponse(false, null, errorMsg);
    }
  }

  /**
   * Save captured data
   */
  async saveCapturedData(accountId, capturedData, options = {}) {
    const { showLoadingMessage = true } = options;
    const operationTimer = LogUtils.logDataCapture('save_start', accountId);
    
    try {
      logger.info(LOG_CATEGORIES.DATA_CAPTURE, `Starting save for account: ${accountId}`);

      if (showLoadingMessage) {
        message.loading('💾 Saving profile data...', 0);
      }

      const apiTimer = LogUtils.logApiCall('POST', '/api/saveProfileData', { accountId });
      const response = await apiService.saveProfileData(accountId, capturedData);
      apiTimer.end();

      if (showLoadingMessage) message.destroy();

      if (isSuccessResponse(response.data)) {
        const savedData = extractDataFromResponse(response.data);
        
        logger.info(LOG_CATEGORIES.DATA_CAPTURE, `Save successful for account: ${accountId}`);

        if (showLoadingMessage) {
          message.success('✅ Profile data saved successfully!');
        }

        operationTimer.end();
        return createStandardResponse(true, savedData, 'Save successful');
      } else {
        const errorMsg = getErrorMessage(response.data, 'Save failed');
        logger.error(LOG_CATEGORIES.DATA_CAPTURE, `Save failed for account: ${accountId}`, {
          error: errorMsg
        });

        operationTimer.end();
        return createStandardResponse(false, null, errorMsg);
      }

    } catch (error) {
      if (showLoadingMessage) message.destroy();
      
      logger.error(LOG_CATEGORIES.DATA_CAPTURE, `Save error for account: ${accountId}`, {
        error: error.message
      });

      const errorMsg = error.response?.data?.message || error.message || 'Save failed';
      
      if (showLoadingMessage) {
        message.error(`❌ Save failed: ${errorMsg}`);
      }

      operationTimer.end();
      return createStandardResponse(false, null, errorMsg);
    }
  }

  /**
   * Complete capture and save flow
   */
  async captureAndSave(profile, options = {}) {
    const { showLoadingMessage = true } = options;
    const operationTimer = LogUtils.logUserAction('capture_and_save', { profileId: profile.id });
    
    try {
      logger.info(LOG_CATEGORIES.DATA_CAPTURE, `Starting capture and save flow for profile: ${profile.id}`);

      // Step 1: Capture
      const captureResult = await this.captureBrowserData(profile, { 
        showLoadingMessage: false 
      });

      if (!captureResult.success) {
        logger.error(LOG_CATEGORIES.DATA_CAPTURE, `Capture failed in flow for profile: ${profile.id}`);
        if (showLoadingMessage) {
          message.error(`❌ Capture failed: ${captureResult.message}`);
        }
        operationTimer.end();
        return captureResult;
      }

      // Step 2: Save
      const saveResult = await this.saveCapturedData(profile.account_id, captureResult.data, { 
        showLoadingMessage: false 
      });

      if (!saveResult.success) {
        logger.error(LOG_CATEGORIES.DATA_CAPTURE, `Save failed in flow for profile: ${profile.id}`);
        if (showLoadingMessage) {
          message.error(`❌ Save failed: ${saveResult.message}`);
        }
        operationTimer.end();
        return saveResult;
      }

      logger.info(LOG_CATEGORIES.DATA_CAPTURE, `Capture and save flow completed for profile: ${profile.id}`);
      
      if (showLoadingMessage) {
        message.success('✅ Profile data captured and saved successfully!');
      }

      operationTimer.end();
      return createStandardResponse(true, saveResult.data, 'Capture and save completed successfully');

    } catch (error) {
      logger.error(LOG_CATEGORIES.DATA_CAPTURE, `Capture and save flow error for profile: ${profile.id}`, {
        error: error.message
      });

      if (showLoadingMessage) {
        message.error(`❌ Operation failed: ${error.message}`);
      }

      operationTimer.end();
      return createStandardResponse(false, null, error.message);
    }
  }
}

/**
 * Global unified profile manager instance
 */
export const unifiedProfileManager = new UnifiedProfileManager();

/**
 * Simplified API for components
 */
export const ProfileAPI = {
  captureAndSave: (profile, options) => unifiedProfileManager.captureAndSave(profile, options),
  capture: (profile, options) => unifiedProfileManager.captureBrowserData(profile, options),
  save: (accountId, data, options) => unifiedProfileManager.saveCapturedData(accountId, data, options),
  getAvailableProfiles: () => unifiedProfileManager.getAvailableProfiles(),
  ensureExists: (profile) => unifiedProfileManager.ensureProfileExists(profile)
};

// Make available globally in development
if (process.env.NODE_ENV === 'development') {
  window.UnifiedProfileManager = {
    unifiedProfileManager,
    ProfileAPI,
    captureAndSave: ProfileAPI.captureAndSave,
    getAvailableProfiles: ProfileAPI.getAvailableProfiles
  };
}
