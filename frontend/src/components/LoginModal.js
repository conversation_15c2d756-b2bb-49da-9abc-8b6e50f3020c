/**
 * Login Modal Component - Handles user authentication
 */

import React, { useState } from 'react';
import { Modal, Form, Input, Button, Alert, Typography, Divider } from 'antd';
import { UserOutlined, LockOutlined, GoogleOutlined } from '@ant-design/icons';
import { useAuth } from '../contexts/AuthContext';

const { Title, Text } = Typography;

const LoginModal = ({ visible, onCancel }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const { login } = useAuth();

  const handleLogin = async (values) => {
    setLoading(true);
    setError('');

    try {
      console.log('🔍 [LOGIN] Attempting login with:', values.email);

      // Call auto-login service
      const response = await fetch('http://localhost:3000/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          email: values.email,
          password: values.password,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ [LOGIN] Login successful:', data);

        // Update auth context
        await login({
          id: data.user?.id || 1,
          email: values.email,
          name: data.user?.name || values.email,
          role: data.user?.role || 'user',
          has_bot_insta_access: data.user?.has_bot_insta_access || true,
        });

        // Close modal
        onCancel();
        form.resetFields();
      } else {
        const errorData = await response.json();
        setError(errorData.message || 'Login failed. Please check your credentials.');
      }
    } catch (error) {
      console.error('❌ [LOGIN] Login error:', error);
      setError('Connection failed. Please make sure the auto-login service is running.');
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleLogin = async () => {
    setLoading(true);
    setError('');

    try {
      console.log('🔍 [LOGIN] Starting Google OAuth flow...');

      // For Electron app, we need to handle OAuth differently
      if (window.electronAPI) {
        // Use Electron's shell to open external browser
        const authUrl = 'http://localhost:3000/auth/google?return_url=http://localhost:3000/auth/success';
        console.log('🔍 [LOGIN] Opening external browser for OAuth:', authUrl);

        // Open in external browser
        window.electronAPI.openExternal(authUrl);

        // Show instructions to user and start polling for auth status
        setError('Please complete the login in your browser. We will automatically detect when you are logged in.');

        // Start polling for authentication status
        const pollInterval = setInterval(async () => {
          try {
            console.log('🔍 [LOGIN] Polling for auth status...');
            const response = await fetch('http://localhost:3000/auth/verify', {
              method: 'GET',
              credentials: 'include',
            });

            if (response.ok) {
              const userData = await response.json();
              console.log('✅ [LOGIN] User authenticated via OAuth:', userData);

              clearInterval(pollInterval);
              setError('');

              // Update auth context
              await login({
                id: userData.user.id,
                email: userData.user.email,
                name: userData.user.name || userData.user.email,
                role: userData.user.role || 'user',
                has_bot_insta_access: userData.user.has_bot_insta_access || true,
              });

              // Close modal
              onCancel();
            }
          } catch (error) {
            console.log('🔍 [LOGIN] Still waiting for OAuth completion...');
          }
        }, 2000); // Poll every 2 seconds

        // Stop polling after 5 minutes
        setTimeout(() => {
          clearInterval(pollInterval);
          if (error.includes('We will automatically detect')) {
            setError('OAuth timeout. Please try logging in with email/password or try Google login again.');
          }
        }, 300000); // 5 minutes

      } else {
        // Web version - use popup
        const authUrl = 'http://localhost:3000/auth/google';
        const popup = window.open(
          authUrl,
          'google-login',
          'width=500,height=600,scrollbars=yes,resizable=yes'
        );

        if (!popup) {
          setError('Popup was blocked. Please allow popups and try again.');
          return;
        }

        // Listen for messages from popup
        const handleMessage = (event) => {
          if (event.origin !== 'http://localhost:3000') return;

          if (event.data.type === 'AUTH_SUCCESS') {
            console.log('✅ [LOGIN] Google OAuth successful');
            popup.close();
            window.removeEventListener('message', handleMessage);

            // Check auth status after successful OAuth
            setTimeout(async () => {
              try {
                const response = await fetch('http://localhost:3000/auth/verify', {
                  method: 'GET',
                  credentials: 'include',
                });

                if (response.ok) {
                  const userData = await response.json();
                  console.log('✅ [LOGIN] User data received:', userData);

                  // Update auth context
                  await login({
                    id: userData.id,
                    email: userData.email,
                    name: userData.name || userData.email,
                    role: userData.role || 'user',
                    has_bot_insta_access: userData.has_bot_insta_access || true,
                  });

                  // Close modal
                  onCancel();
                } else {
                  setError('Authentication verification failed. Please try logging in with email/password.');
                }
              } catch (error) {
                console.error('❌ [LOGIN] Auth verification error:', error);
                setError('Authentication verification failed. Please try logging in with email/password.');
              }
            }, 1000);

          } else if (event.data.type === 'AUTH_FAILED') {
            console.error('❌ [LOGIN] Google OAuth failed');
            popup.close();
            window.removeEventListener('message', handleMessage);
            setError('Google authentication failed. Please try again.');
          }
        };

        window.addEventListener('message', handleMessage);

        // Monitor popup for manual close
        const checkClosed = setInterval(() => {
          if (popup.closed) {
            clearInterval(checkClosed);
            window.removeEventListener('message', handleMessage);
            console.log('🔍 [LOGIN] Popup closed manually');
          }
        }, 1000);
      }
    } catch (error) {
      console.error('❌ [LOGIN] Google OAuth error:', error);
      setError('Google login failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fillTestCredentials = () => {
    form.setFieldsValue({
      email: '<EMAIL>',
      password: 'desktop123',
    });
  };

  return (
    <Modal
      title={null}
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={400}
      centered
    >
      <div style={{ padding: '20px 0' }}>
        <Title level={3} style={{ textAlign: 'center', marginBottom: 30 }}>
          Login to Facebook Automation
        </Title>

        {error && (
          <Alert
            message={error}
            type="error"
            style={{ marginBottom: 20 }}
            closable
            onClose={() => setError('')}
          />
        )}

        <Form
          form={form}
          onFinish={handleLogin}
          layout="vertical"
          size="large"
        >
          <Form.Item
            name="email"
            label="Email"
            rules={[
              { required: true, message: 'Please input your email!' },
              { type: 'email', message: 'Please enter a valid email!' },
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="Enter your email"
              autoComplete="email"
            />
          </Form.Item>

          <Form.Item
            name="password"
            label="Password"
            rules={[
              { required: true, message: 'Please input your password!' },
              { min: 6, message: 'Password must be at least 6 characters!' },
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="Enter your password"
              autoComplete="current-password"
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              block
              style={{ height: 45 }}
            >
              {loading ? 'Logging in...' : 'Login'}
            </Button>
          </Form.Item>
        </Form>

        <Divider>or</Divider>

        <Button
          icon={<GoogleOutlined />}
          onClick={handleGoogleLogin}
          block
          style={{ height: 45, marginBottom: 15 }}
        >
          Continue with Google
        </Button>

        <div style={{ textAlign: 'center', marginTop: 20 }}>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            For desktop development, use test credentials:
          </Text>
          <br />
          <Button
            type="link"
            size="small"
            onClick={fillTestCredentials}
            style={{ padding: 0, height: 'auto', fontSize: '12px' }}
          >
            <EMAIL> / desktop123
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default LoginModal;
