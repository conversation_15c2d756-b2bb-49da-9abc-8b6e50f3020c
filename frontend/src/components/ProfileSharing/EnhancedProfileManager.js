import React, { useState, useEffect } from 'react';
import { Ta<PERSON>, Badge, Card, message } from 'antd';
import {
  UserOutlined,
  ShareAltOutlined,
  SyncOutlined,
  SettingOutlined
} from '@ant-design/icons';
import AdminProfileSharing from './AdminProfileSharing';
import UserSharedProfiles from './UserSharedProfiles';
import ProfileSyncStatus from './ProfileSyncStatus';
import { apiService } from '../../services/api';

const EnhancedProfileManager = () => {
  const [activeTab, setActiveTab] = useState('shared-profiles');
  const [userRole, setUserRole] = useState('user');
  const [accessSummary, setAccessSummary] = useState({});
  const [syncStats, setSyncStats] = useState({});

  useEffect(() => {
    loadUserRole();
    loadAccessSummary();
    loadSyncStats();
  }, []);

  const loadUserRole = async () => {
    try {
      // This should get user role from auth context or API
      // For now, we'll check if user has admin access by trying to access admin endpoints
      try {
        await apiService.get('/profile-sharing/statistics');
        setUserRole('admin');
      } catch (error) {
        setUserRole('user');
      }
    } catch (error) {
      console.error('Failed to load user role:', error);
      setUserRole('user');
    }
  };

  const loadAccessSummary = async () => {
    try {
      const response = await apiService.get('/profiles/access-summary');
      if (response.success) {
        setAccessSummary(response.data);
      }
    } catch (error) {
      console.error('Failed to load access summary:', error);
    }
  };

  const loadSyncStats = async () => {
    try {
      const response = await apiService.get('/profile-sharing/sync/active-sessions');
      if (response.success) {
        setSyncStats({
          activeSessions: response.data.active_sessions?.length || 0
        });
      }
    } catch (error) {
      console.error('Failed to load sync stats:', error);
    }
  };

  const getTabItems = () => {
    const items = [
      {
        key: 'shared-profiles',
        label: (
          <span>
            <UserOutlined />
            My Profiles
            {accessSummary.total_accessible > 0 && (
              <Badge 
                count={accessSummary.total_accessible} 
                style={{ marginLeft: 8 }}
                showZero={false}
              />
            )}
          </span>
        ),
        children: <UserSharedProfiles />
      },
      {
        key: 'sync-status',
        label: (
          <span>
            <SyncOutlined />
            Sync Status
            {syncStats.activeSessions > 0 && (
              <Badge 
                count={syncStats.activeSessions} 
                status="processing"
                style={{ marginLeft: 8 }}
              />
            )}
          </span>
        ),
        children: <ProfileSyncStatus />
      }
    ];

    // Add admin-only tabs
    if (userRole === 'admin') {
      items.push({
        key: 'admin-sharing',
        label: (
          <span>
            <ShareAltOutlined />
            Admin Sharing
          </span>
        ),
        children: <AdminProfileSharing />
      });
    }

    return items;
  };

  const handleTabChange = (key) => {
    setActiveTab(key);
    
    // Refresh data when switching tabs
    if (key === 'shared-profiles') {
      loadAccessSummary();
    } else if (key === 'sync-status') {
      loadSyncStats();
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card
        title="Enhanced Profile Manager"
        extra={
          <div style={{ fontSize: '14px', color: '#666' }}>
            Role: <strong>{userRole.toUpperCase()}</strong>
          </div>
        }
      >
        <Tabs
          activeKey={activeTab}
          onChange={handleTabChange}
          items={getTabItems()}
          size="large"
          tabBarStyle={{ marginBottom: '24px' }}
        />
      </Card>
    </div>
  );
};

export default EnhancedProfileManager;
