import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Select,
  DatePicker,
  Switch,
  message,
  Space,
  Tag,
  Tooltip,
  Popconfirm,
  Row,
  Col,
  Statistic,
  Alert
} from 'antd';
import {
  ShareAltOutlined,
  UserOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  StopOutlined,
  DeleteOutlined,
  PlusOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import moment from 'moment';
import { apiService } from '../../services/api';

const { Option } = Select;

const AdminProfileSharing = () => {
  const [loading, setLoading] = useState(false);
  const [shares, setShares] = useState([]);
  const [accounts, setAccounts] = useState([]);
  const [users, setUsers] = useState([]);
  const [statistics, setStatistics] = useState({});
  const [shareModalVisible, setShareModalVisible] = useState(false);
  const [bulkShareModalVisible, setBulkShareModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [bulkForm] = Form.useForm();

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        loadShares(),
        loadAccounts(),
        loadUsers(),
        loadStatistics()
      ]);
    } catch (error) {
      message.error('Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const loadShares = async () => {
    try {
      const response = await apiService.get('/profiles/statistics');
      if (response.success) {
        // This would need to be adapted based on actual API response structure
        setShares(response.data.shares || []);
      }
    } catch (error) {
      console.error('Failed to load shares:', error);
    }
  };

  const loadAccounts = async () => {
    try {
      const response = await apiService.get('/accounts');
      if (response.success) {
        setAccounts(response.data || []);
      }
    } catch (error) {
      console.error('Failed to load accounts:', error);
    }
  };

  const loadUsers = async () => {
    try {
      const response = await apiService.get('/users');
      if (response.success) {
        setUsers(response.data || []);
      }
    } catch (error) {
      console.error('Failed to load users:', error);
    }
  };

  const loadStatistics = async () => {
    try {
      const response = await apiService.get('/profile-sharing/statistics');
      if (response.success) {
        setStatistics(response.data || {});
      }
    } catch (error) {
      console.error('Failed to load statistics:', error);
    }
  };

  const handleShareProfile = async (values) => {
    try {
      const shareData = {
        account_id: values.account_id,
        shared_with_user_id: values.user_id,
        permissions: {
          can_modify_profile: values.can_modify_profile || false,
          can_view_history: values.can_view_history !== false,
          can_export_data: values.can_export_data || false,
          access_level: values.access_level || 'read'
        },
        expires_at: values.expires_at ? values.expires_at.toISOString() : null
      };

      const response = await apiService.post('/profile-sharing/share', shareData);
      
      if (response.success) {
        message.success('Profile shared successfully');
        setShareModalVisible(false);
        form.resetFields();
        loadShares();
      } else {
        message.error(response.message || 'Failed to share profile');
      }
    } catch (error) {
      message.error('Failed to share profile');
      console.error('Share error:', error);
    }
  };

  const handleBulkShare = async (values) => {
    try {
      const bulkData = {
        accountIds: values.account_ids,
        userIds: values.user_ids,
        permissions: {
          can_modify_profile: values.can_modify_profile || false,
          can_view_history: values.can_view_history !== false,
          can_export_data: values.can_export_data || false,
          access_level: values.access_level || 'read'
        },
        expiresAt: values.expires_at ? values.expires_at.toISOString() : null
      };

      const response = await apiService.post('/profiles/bulk-share', bulkData);
      
      if (response.success) {
        const { successful, failed } = response.data.summary;
        message.success(`Bulk sharing completed: ${successful} successful, ${failed} failed`);
        setBulkShareModalVisible(false);
        bulkForm.resetFields();
        loadShares();
      } else {
        message.error(response.message || 'Failed to bulk share profiles');
      }
    } catch (error) {
      message.error('Failed to bulk share profiles');
      console.error('Bulk share error:', error);
    }
  };

  const handleRevokeShare = async (shareId) => {
    try {
      const response = await apiService.delete(`/profiles/share/${shareId}`);
      
      if (response.success) {
        message.success('Profile share revoked successfully');
        loadShares();
      } else {
        message.error(response.message || 'Failed to revoke share');
      }
    } catch (error) {
      message.error('Failed to revoke share');
      console.error('Revoke error:', error);
    }
  };

  const getStatusTag = (status, expiresAt) => {
    if (status === 'revoked') {
      return <Tag color="red">Revoked</Tag>;
    }
    
    if (expiresAt && moment(expiresAt).isBefore(moment())) {
      return <Tag color="orange">Expired</Tag>;
    }
    
    if (status === 'active') {
      return <Tag color="green">Active</Tag>;
    }
    
    return <Tag color="default">{status}</Tag>;
  };

  const columns = [
    {
      title: 'Account',
      dataIndex: 'account',
      key: 'account',
      render: (account) => (
        <div>
          <div>{account?.name || 'Unknown Account'}</div>
          <small style={{ color: '#666' }}>{account?.website_url}</small>
        </div>
      )
    },
    {
      title: 'Shared With',
      dataIndex: 'sharedWithUser',
      key: 'sharedWithUser',
      render: (user) => (
        <div>
          <UserOutlined /> {user?.email || 'Unknown User'}
        </div>
      )
    },
    {
      title: 'Permissions',
      dataIndex: 'permissions',
      key: 'permissions',
      render: (permissions) => (
        <Space direction="vertical" size="small">
          <Tag color={permissions?.can_modify_profile ? 'green' : 'default'}>
            {permissions?.can_modify_profile ? 'Can Modify' : 'Read Only'}
          </Tag>
          <Tag color={permissions?.can_export_data ? 'blue' : 'default'}>
            {permissions?.can_export_data ? 'Can Export' : 'No Export'}
          </Tag>
        </Space>
      )
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status, record) => getStatusTag(status, record.expires_at)
    },
    {
      title: 'Expires',
      dataIndex: 'expires_at',
      key: 'expires_at',
      render: (expiresAt) => (
        expiresAt ? (
          <Tooltip title={moment(expiresAt).format('YYYY-MM-DD HH:mm:ss')}>
            <ClockCircleOutlined /> {moment(expiresAt).fromNow()}
          </Tooltip>
        ) : (
          <Tag color="blue">Never</Tag>
        )
      )
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Popconfirm
            title="Are you sure you want to revoke this share?"
            onConfirm={() => handleRevokeShare(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Button 
              type="link" 
              danger 
              icon={<StopOutlined />}
              disabled={record.status === 'revoked'}
            >
              Revoke
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Shares"
              value={statistics.totalShares || 0}
              prefix={<ShareAltOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Active Shares"
              value={statistics.activeShares || 0}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Expired Shares"
              value={statistics.expiredShares || 0}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Revoked Shares"
              value={statistics.revokedShares || 0}
              prefix={<StopOutlined />}
              valueStyle={{ color: '#666' }}
            />
          </Card>
        </Col>
      </Row>

      <Card
        title="Profile Sharing Management"
        extra={
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setShareModalVisible(true)}
            >
              Share Profile
            </Button>
            <Button
              icon={<ShareAltOutlined />}
              onClick={() => setBulkShareModalVisible(true)}
            >
              Bulk Share
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={loadData}
              loading={loading}
            >
              Refresh
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={shares}
          loading={loading}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `Total ${total} shares`
          }}
        />
      </Card>

      {/* Share Profile Modal */}
      <Modal
        title="Share Profile"
        open={shareModalVisible}
        onCancel={() => {
          setShareModalVisible(false);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleShareProfile}
        >
          <Form.Item
            name="account_id"
            label="Account"
            rules={[{ required: true, message: 'Please select an account' }]}
          >
            <Select
              placeholder="Select account to share"
              showSearch
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {accounts.map(account => (
                <Option key={account.id} value={account.id}>
                  {account.name || account.website_url}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="user_id"
            label="Share With User"
            rules={[{ required: true, message: 'Please select a user' }]}
          >
            <Select
              placeholder="Select user to share with"
              showSearch
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {users.map(user => (
                <Option key={user.id} value={user.id}>
                  {user.email}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item name="access_level" label="Access Level" initialValue="read">
            <Select>
              <Option value="read">Read Only</Option>
              <Option value="write">Read & Write</Option>
              <Option value="full">Full Access</Option>
            </Select>
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="can_view_history" valuePropName="checked" initialValue={true}>
                <Switch checkedChildren="Can View History" unCheckedChildren="No History Access" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="can_modify_profile" valuePropName="checked">
                <Switch checkedChildren="Can Modify" unCheckedChildren="Read Only" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="can_export_data" valuePropName="checked">
                <Switch checkedChildren="Can Export" unCheckedChildren="No Export" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="expires_at" label="Expires At (Optional)">
            <DatePicker
              showTime
              placeholder="Select expiration date"
              style={{ width: '100%' }}
              disabledDate={(current) => current && current < moment().endOf('day')}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                Share Profile
              </Button>
              <Button onClick={() => {
                setShareModalVisible(false);
                form.resetFields();
              }}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Bulk Share Modal */}
      <Modal
        title="Bulk Share Profiles"
        open={bulkShareModalVisible}
        onCancel={() => {
          setBulkShareModalVisible(false);
          bulkForm.resetFields();
        }}
        footer={null}
        width={700}
      >
        <Alert
          message="Bulk Share Warning"
          description="This will create multiple profile shares. Make sure users have appropriate packages before sharing."
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
        
        <Form
          form={bulkForm}
          layout="vertical"
          onFinish={handleBulkShare}
        >
          <Form.Item
            name="account_ids"
            label="Accounts"
            rules={[{ required: true, message: 'Please select accounts' }]}
          >
            <Select
              mode="multiple"
              placeholder="Select accounts to share"
              showSearch
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {accounts.map(account => (
                <Option key={account.id} value={account.id}>
                  {account.name || account.website_url}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="user_ids"
            label="Share With Users"
            rules={[{ required: true, message: 'Please select users' }]}
          >
            <Select
              mode="multiple"
              placeholder="Select users to share with"
              showSearch
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {users.map(user => (
                <Option key={user.id} value={user.id}>
                  {user.email}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item name="access_level" label="Access Level" initialValue="read">
            <Select>
              <Option value="read">Read Only</Option>
              <Option value="write">Read & Write</Option>
              <Option value="full">Full Access</Option>
            </Select>
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                Bulk Share
              </Button>
              <Button onClick={() => {
                setBulkShareModalVisible(false);
                bulkForm.resetFields();
              }}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AdminProfileSharing;
