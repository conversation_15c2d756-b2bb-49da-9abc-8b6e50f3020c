/**
 * UserAssignmentForm Component - Form for assigning users to profile groups
 */

import React, { useState, useEffect } from 'react';
import { 
  Form, 
  Select, 
  Button, 
  message, 
  Modal, 
  Space, 
  Checkbox, 
  DatePicker,
  Card,
  Typography 
} from 'antd';
import { UserAddOutlined, EditOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';

const { Option } = Select;
const { Text } = Typography;

const UserAssignmentForm = ({ 
  visible, 
  onCancel, 
  onSuccess, 
  editingAssignment = null,
  users = [],
  profileGroups = [],
  loading = false 
}) => {
  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    if (visible) {
      if (editingAssignment) {
        form.setFieldsValue({
          user_id: editingAssignment.user_id,
          profile_group_id: editingAssignment.profile_group_id,
          expires_at: editingAssignment.expires_at ? dayjs(editingAssignment.expires_at) : null,
          permissions: editingAssignment.permissions || {
            can_launch_browser: true,
            can_view_profiles: true,
            can_export_data: false,
            access_level: 'read',
          },
        });
      } else {
        form.resetFields();
        form.setFieldsValue({
          permissions: {
            can_launch_browser: true,
            can_view_profiles: true,
            can_export_data: false,
            access_level: 'read',
          },
        });
      }
    }
  }, [visible, editingAssignment, form]);

  const handleSubmit = async (values) => {
    setSubmitting(true);
    try {
      const submitData = {
        ...values,
        expires_at: values.expires_at ? values.expires_at.toISOString() : null,
      };

      const url = editingAssignment 
        ? `http://localhost:3000/profiles/group-access/${editingAssignment.id}`
        : 'http://localhost:3000/profiles/group-access';
      
      const method = editingAssignment ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(submitData),
      });

      if (response.ok) {
        const data = await response.json();
        message.success(
          editingAssignment 
            ? 'User assignment updated successfully!' 
            : 'User assigned to profile group successfully!'
        );
        form.resetFields();
        onSuccess(data.data);
      } else {
        const errorData = await response.json();
        message.error(errorData.message || 'Operation failed');
      }
    } catch (error) {
      console.error('User assignment operation error:', error);
      message.error('Network error occurred');
    } finally {
      setSubmitting(false);
    }
  };

  // Filter active users only
  const activeUsers = users.filter(user => user.status === 'active');

  return (
    <Modal
      title={
        <Space>
          {editingAssignment ? <EditOutlined /> : <UserAddOutlined />}
          {editingAssignment ? 'Edit User Assignment' : 'Assign User to Profile Group'}
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={700}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        size="large"
      >
        <Form.Item
          name="user_id"
          label="User"
          rules={[
            { required: true, message: 'Please select a user!' },
          ]}
        >
          <Select
            placeholder="Select user (active users only)"
            showSearch
            optionFilterProp="children"
            disabled={!!editingAssignment}
          >
            {activeUsers.map(user => (
              <Option key={user.id} value={user.id}>
                {user.email} {user.name && `(${user.name})`}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="profile_group_id"
          label="Profile Group"
          rules={[
            { required: true, message: 'Please select a profile group!' },
          ]}
        >
          <Select
            placeholder="Select profile group"
            showSearch
            optionFilterProp="children"
            disabled={!!editingAssignment}
          >
            {profileGroups.map(group => (
              <Option key={group.id} value={group.id}>
                {group.name}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="expires_at"
          label="Access Expiration (Optional)"
        >
          <DatePicker
            showTime
            placeholder="Select expiration date and time"
            style={{ width: '100%' }}
            disabledDate={(current) => current && current < dayjs().startOf('day')}
          />
        </Form.Item>

        <Card title="Access Permissions" size="small" style={{ marginBottom: 16 }}>
          <Form.Item name={['permissions', 'access_level']} label="Access Level">
            <Select placeholder="Select access level">
              <Option value="read">Read Only</Option>
              <Option value="write">Read & Write</Option>
              <Option value="full">Full Access</Option>
            </Select>
          </Form.Item>

          <Form.Item name={['permissions', 'can_launch_browser']} valuePropName="checked">
            <Checkbox>Can launch browser</Checkbox>
          </Form.Item>

          <Form.Item name={['permissions', 'can_view_profiles']} valuePropName="checked">
            <Checkbox>Can view profiles</Checkbox>
          </Form.Item>

          <Form.Item name={['permissions', 'can_export_data']} valuePropName="checked">
            <Checkbox>Can export data</Checkbox>
          </Form.Item>
        </Card>

        <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
          <Space>
            <Button onClick={onCancel}>
              Cancel
            </Button>
            <Button 
              type="primary" 
              htmlType="submit" 
              loading={submitting}
              icon={editingAssignment ? <EditOutlined /> : <UserAddOutlined />}
            >
              {editingAssignment ? 'Update Assignment' : 'Assign User'}
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default UserAssignmentForm;
