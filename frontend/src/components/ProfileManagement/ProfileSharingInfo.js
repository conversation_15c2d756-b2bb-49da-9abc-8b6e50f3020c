/**
 * ProfileSharingInfo Component - Display information about profile sharing flow
 */

import React from 'react';
import { 
  Card, 
  Steps, 
  Typography, 
  Space, 
  Tag, 
  Alert,
  Divider,
  List
} from 'antd';
import { 
  UserOutlined, 
  ChromeOutlined, 
  SaveOutlined, 
  ShareAltOutlined,
  DatabaseOutlined,
  TeamOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;

const ProfileSharingInfo = () => {
  const adminSteps = [
    {
      title: 'Launch Browser',
      description: '<PERSON>min launches Camoufox antidetect browser',
      icon: <ChromeOutlined />,
      status: 'process'
    },
    {
      title: 'Login to Account',
      description: 'Admin logs into Facebook/Instagram account',
      icon: <UserOutlined />,
      status: 'process'
    },
    {
      title: 'Save Profile Data',
      description: 'Admin saves localStorage, IndexedDB, History, Cookies to database',
      icon: <SaveOutlined />,
      status: 'process'
    },
    {
      title: 'Share with Group',
      description: 'Profile data is shared with assigned profile group',
      icon: <ShareAltOutlined />,
      status: 'process'
    }
  ];

  const userSteps = [
    {
      title: 'Purchase Package',
      description: 'User purchases package with "has_bot_insta" access',
      icon: <CheckCircleOutlined />,
      status: 'finish'
    },
    {
      title: 'Login Successfully',
      description: 'User logs into the system',
      icon: <UserOutlined />,
      status: 'finish'
    },
    {
      title: 'Access Profile Group',
      description: 'User gains access to shared profile groups',
      icon: <TeamOutlined />,
      status: 'process'
    },
    {
      title: 'Launch Shared Profile',
      description: 'User launches browser with saved profile data (no login required)',
      icon: <DatabaseOutlined />,
      status: 'process'
    }
  ];

  const features = [
    {
      title: 'localStorage Sharing',
      description: 'User preferences, session data, and application state',
      icon: '🗄️'
    },
    {
      title: 'IndexedDB Sharing',
      description: 'Complex data structures and offline storage',
      icon: '💾'
    },
    {
      title: 'Browsing History',
      description: 'Previously visited URLs and navigation history',
      icon: '📚'
    },
    {
      title: 'Cookies Sharing',
      description: 'Authentication cookies and session information',
      icon: '🍪'
    }
  ];

  return (
    <Space direction="vertical" style={{ width: '100%' }} size="large">
      <Card>
        <Title level={3}>
          <ShareAltOutlined /> Profile Sharing Flow
        </Title>
        <Paragraph>
          This system enables admins to share logged-in browser profiles with users, 
          allowing seamless access to social media accounts without requiring individual logins.
        </Paragraph>
      </Card>

      <Card title="Admin Workflow" extra={<Tag color="red">Admin Only</Tag>}>
        <Steps direction="vertical" size="small" current={-1}>
          {adminSteps.map((step, index) => (
            <Step
              key={index}
              title={step.title}
              description={step.description}
              icon={step.icon}
              status={step.status}
            />
          ))}
        </Steps>
      </Card>

      <Card title="User Workflow" extra={<Tag color="blue">User Access</Tag>}>
        <Steps direction="vertical" size="small" current={-1}>
          {userSteps.map((step, index) => (
            <Step
              key={index}
              title={step.title}
              description={step.description}
              icon={step.icon}
              status={step.status}
            />
          ))}
        </Steps>
      </Card>

      <Card title="Shared Profile Data">
        <List
          grid={{ gutter: 16, column: 2 }}
          dataSource={features}
          renderItem={item => (
            <List.Item>
              <Card size="small">
                <Space>
                  <span style={{ fontSize: '24px' }}>{item.icon}</span>
                  <div>
                    <Text strong>{item.title}</Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {item.description}
                    </Text>
                  </div>
                </Space>
              </Card>
            </List.Item>
          )}
        />
      </Card>

      <Alert
        message="Security & Access Control"
        description={
          <Space direction="vertical" size="small">
            <Text>• Only users with active status and "has_bot_insta" package can access shared profiles</Text>
            <Text>• Admin accounts bypass all access restrictions</Text>
            <Text>• Profile data is encrypted and stored securely in the database</Text>
            <Text>• Access logs are maintained for audit purposes</Text>
          </Space>
        }
        type="info"
        showIcon
      />

      <Card title="Technical Implementation">
        <Space direction="vertical" style={{ width: '100%' }}>
          <Divider orientation="left">Backend APIs</Divider>
          <List size="small">
            <List.Item>
              <Text code>POST /profiles/{'{accountId}'}/launch-browser</Text> - Launch browser with profile
            </List.Item>
            <List.Item>
              <Text code>POST /profiles/{'{accountId}'}/save-profile</Text> - Save profile data to database
            </List.Item>
            <List.Item>
              <Text code>GET /profiles/{'{accountId}'}/profile-data</Text> - Retrieve saved profile data
            </List.Item>
          </List>

          <Divider orientation="left">Database Storage</Divider>
          <List size="small">
            <List.Item>
              <Text>Profile data stored in <Text code>accounts.cookie_data</Text> field as JSON</Text>
            </List.Item>
            <List.Item>
              <Text>Sync status tracked in <Text code>accounts.sync_status</Text> field</Text>
            </List.Item>
            <List.Item>
              <Text>Access control via <Text code>user_profile_group_access</Text> table</Text>
            </List.Item>
          </List>

          <Divider orientation="left">Browser Integration</Divider>
          <List size="small">
            <List.Item>
              <Text>Camoufox antidetect browser for enhanced privacy</Text>
            </List.Item>
            <List.Item>
              <Text>Automatic profile data extraction and restoration</Text>
            </List.Item>
            <List.Item>
              <Text>Seamless session continuity across users</Text>
            </List.Item>
          </List>
        </Space>
      </Card>
    </Space>
  );
};

export default ProfileSharingInfo;
