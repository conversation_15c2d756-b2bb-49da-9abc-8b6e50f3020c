/**
 * ProfileGroupForm Component - Form for creating/editing profile groups
 */

import React, { useState, useEffect } from 'react';
import { Form, Input, Button, message, Modal, Space } from 'antd';
import { PlusOutlined, EditOutlined } from '@ant-design/icons';
import { apiService } from '../../services/api';

const { TextArea } = Input;

const ProfileGroupForm = ({ 
  visible, 
  onCancel, 
  onSuccess, 
  editingGroup = null,
  loading = false 
}) => {
  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    if (visible) {
      if (editingGroup) {
        form.setFieldsValue({
          name: editingGroup.name,
          description: editingGroup.description,
        });
      } else {
        form.resetFields();
      }
    }
  }, [visible, editingGroup, form]);

  const handleSubmit = async (values) => {
    setSubmitting(true);
    try {
      let response;
      if (editingGroup) {
        response = await apiService.updateProfileGroup(editingGroup.id, values);
      } else {
        response = await apiService.createProfileGroup(values);
      }

      message.success(
        editingGroup
          ? 'Profile group updated successfully!'
          : 'Profile group created successfully!'
      );
      form.resetFields();
      onSuccess(response.data);
    } catch (error) {
      console.error('Profile group operation error:', error);
      message.error(error.message || 'Operation failed');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Modal
      title={
        <Space>
          {editingGroup ? <EditOutlined /> : <PlusOutlined />}
          {editingGroup ? 'Edit Profile Group' : 'Create New Profile Group'}
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={600}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        size="large"
      >
        <Form.Item
          name="name"
          label="Group Name"
          rules={[
            { required: true, message: 'Please enter group name!' },
            { max: 255, message: 'Group name must be less than 255 characters!' },
          ]}
        >
          <Input 
            placeholder="Enter profile group name (e.g., Marketing Team, Sales Team)"
            showCount
            maxLength={255}
          />
        </Form.Item>

        <Form.Item
          name="description"
          label="Description"
          rules={[
            { max: 1000, message: 'Description must be less than 1000 characters!' },
          ]}
        >
          <TextArea
            placeholder="Enter group description (optional)"
            rows={4}
            showCount
            maxLength={1000}
          />
        </Form.Item>

        <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
          <Space>
            <Button onClick={onCancel}>
              Cancel
            </Button>
            <Button 
              type="primary" 
              htmlType="submit" 
              loading={submitting}
              icon={editingGroup ? <EditOutlined /> : <PlusOutlined />}
            >
              {editingGroup ? 'Update Group' : 'Create Group'}
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ProfileGroupForm;
