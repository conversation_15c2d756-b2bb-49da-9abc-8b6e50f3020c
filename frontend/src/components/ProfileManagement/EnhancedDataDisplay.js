/**
 * Enhanced Data Display Component
 * Displays captured browser data with improved formatting and organization
 */

import React, { useState } from 'react';
import { 
  Card, 
  Tabs, 
  Typography, 
  Space, 
  Tag, 
  Descriptions, 
  Table, 
  Collapse,
  Progress,
  Statistic,
  Row,
  Col,
  Alert,
  Button,
  Tooltip
} from 'antd';
import {
  DatabaseOutlined,
  HistoryOutlined,
  SettingOutlined,
  GlobalOutlined,
  InfoCircleOutlined,
  DownloadOutlined,
  ShareAltOutlined,
  CompressOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { Text, Paragraph, Title } = Typography;
const { TabPane } = Tabs;
const { Panel } = Collapse;

const EnhancedDataDisplay = ({ profileData, profile, onShare, onDownload }) => {
  const [activeTab, setActiveTab] = useState('summary');

  if (!profileData) {
    return (
      <Card>
        <Alert
          message="No Data Available"
          description="No profile data has been captured yet. Please capture browser data first."
          type="info"
          showIcon
        />
      </Card>
    );
  }

  const renderSummaryTab = () => {
    const summary = profileData.summary || {};
    const metadata = profileData.metadata || {};
    
    return (
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* Capture Statistics */}
        <Card title="Capture Statistics" size="small">
          <Row gutter={16}>
            <Col span={6}>
              <Statistic
                title="LocalStorage Items"
                value={summary.localStorage_items || 0}
                prefix={<DatabaseOutlined />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="IndexedDB Databases"
                value={summary.indexedDB_databases || 0}
                prefix={<DatabaseOutlined />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="Cookies"
                value={summary.cookies_count || 0}
                prefix={<SettingOutlined />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="Visited Domains"
                value={summary.visited_domains || 0}
                prefix={<GlobalOutlined />}
              />
            </Col>
          </Row>
        </Card>

        {/* Data Size Information */}
        <Card title="Data Size Information" size="small">
          <Descriptions bordered size="small">
            <Descriptions.Item label="Total Size">
              {formatBytes(summary.total_size_bytes || 0)}
            </Descriptions.Item>
            <Descriptions.Item label="Capture Time">
              {summary.capture_timestamp ? dayjs(summary.capture_timestamp).format('YYYY-MM-DD HH:mm:ss') : 'Unknown'}
            </Descriptions.Item>
            <Descriptions.Item label="Capture Version">
              <Tag color="blue">{metadata.capture_version || '1.0'}</Tag>
            </Descriptions.Item>
            <Descriptions.Item label="Data Types" span={3}>
              <Space wrap>
                {(metadata.data_types || []).map(type => (
                  <Tag key={type} color="green">{type}</Tag>
                ))}
              </Space>
            </Descriptions.Item>
          </Descriptions>
        </Card>

        {/* Error Information */}
        {summary.has_errors && (
          <Alert
            message="Capture Warnings"
            description="Some data types encountered errors during capture. Check individual tabs for details."
            type="warning"
            showIcon
          />
        )}

        {/* Actions */}
        <Card title="Actions" size="small">
          <Space>
            <Button 
              type="primary" 
              icon={<ShareAltOutlined />}
              onClick={() => onShare && onShare('public')}
            >
              Share Public
            </Button>
            <Button 
              icon={<ShareAltOutlined />}
              onClick={() => onShare && onShare('private')}
            >
              Share Private
            </Button>
            <Button 
              icon={<DownloadOutlined />}
              onClick={() => onDownload && onDownload()}
            >
              Download
            </Button>
          </Space>
        </Card>
      </Space>
    );
  };

  const renderLocalStorageTab = () => {
    const localStorage = profileData.localStorage || {};
    const items = localStorage.items || {};
    const rawItems = localStorage.rawItems || [];

    const columns = [
      {
        title: 'Key',
        dataIndex: 'key',
        key: 'key',
        width: '30%',
        ellipsis: true,
      },
      {
        title: 'Value',
        dataIndex: 'value',
        key: 'value',
        ellipsis: true,
        render: (value) => (
          <Tooltip title={value}>
            <Text code style={{ maxWidth: 300 }}>
              {value && value.length > 100 ? `${value.substring(0, 100)}...` : value}
            </Text>
          </Tooltip>
        )
      },
      {
        title: 'Size',
        key: 'size',
        width: '15%',
        render: (_, record) => formatBytes((record.key?.length || 0) + (record.value?.length || 0))
      }
    ];

    return (
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Card title="LocalStorage Overview" size="small">
          <Descriptions bordered size="small">
            <Descriptions.Item label="Total Items">
              {localStorage.length || 0}
            </Descriptions.Item>
            <Descriptions.Item label="Total Size">
              {formatBytes(localStorage.totalSize || 0)}
            </Descriptions.Item>
            <Descriptions.Item label="Keys Count">
              {(localStorage.keys || []).length}
            </Descriptions.Item>
          </Descriptions>
        </Card>

        {localStorage.error && (
          <Alert
            message="LocalStorage Capture Error"
            description={localStorage.error}
            type="error"
            showIcon
          />
        )}

        <Card title="LocalStorage Items" size="small">
          <Table
            columns={columns}
            dataSource={rawItems.map((item, index) => ({ ...item, key: index }))}
            pagination={{ pageSize: 10 }}
            size="small"
            scroll={{ x: true }}
          />
        </Card>
      </Space>
    );
  };

  const renderIndexedDBTab = () => {
    const indexedDB = profileData.indexedDB || {};
    const databases = indexedDB.databases || [];

    return (
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Card title="IndexedDB Overview" size="small">
          <Descriptions bordered size="small">
            <Descriptions.Item label="Databases Count">
              {databases.length}
            </Descriptions.Item>
            <Descriptions.Item label="Total Size">
              {formatBytes(indexedDB.totalSize || 0)}
            </Descriptions.Item>
          </Descriptions>
        </Card>

        {indexedDB.error && (
          <Alert
            message="IndexedDB Capture Error"
            description={indexedDB.error}
            type="error"
            showIcon
          />
        )}

        {databases.length > 0 ? (
          <Card title="Databases" size="small">
            <Collapse>
              {databases.map((db, index) => (
                <Panel 
                  header={`${db.name} (v${db.version})`} 
                  key={index}
                  extra={<Tag color="blue">{db.objectStores?.length || 0} stores</Tag>}
                >
                  <Descriptions bordered size="small">
                    <Descriptions.Item label="Database Name">
                      {db.name}
                    </Descriptions.Item>
                    <Descriptions.Item label="Version">
                      {db.version}
                    </Descriptions.Item>
                    <Descriptions.Item label="Object Stores">
                      {db.objectStores?.length || 0}
                    </Descriptions.Item>
                  </Descriptions>

                  {db.objectStores && db.objectStores.length > 0 && (
                    <div style={{ marginTop: 16 }}>
                      <Title level={5}>Object Stores</Title>
                      {db.objectStores.map((store, storeIndex) => (
                        <Card key={storeIndex} size="small" style={{ marginBottom: 8 }}>
                          <Descriptions size="small" column={2}>
                            <Descriptions.Item label="Store Name">
                              {store.name}
                            </Descriptions.Item>
                            <Descriptions.Item label="Data Count">
                              {store.dataCount || 0}
                            </Descriptions.Item>
                            <Descriptions.Item label="Key Path">
                              {store.keyPath || 'None'}
                            </Descriptions.Item>
                            <Descriptions.Item label="Auto Increment">
                              {store.autoIncrement ? 'Yes' : 'No'}
                            </Descriptions.Item>
                          </Descriptions>
                          {store.indexNames && store.indexNames.length > 0 && (
                            <div style={{ marginTop: 8 }}>
                              <Text strong>Indexes: </Text>
                              <Space wrap>
                                {store.indexNames.map(indexName => (
                                  <Tag key={indexName} size="small">{indexName}</Tag>
                                ))}
                              </Space>
                            </div>
                          )}
                        </Card>
                      ))}
                    </div>
                  )}
                </Panel>
              ))}
            </Collapse>
          </Card>
        ) : (
          <Alert
            message="No IndexedDB Databases"
            description="No IndexedDB databases were found in this profile."
            type="info"
            showIcon
          />
        )}
      </Space>
    );
  };

  const renderHistoryTab = () => {
    const history = profileData.history || {};
    const visitedDomains = history.visitedDomains || [];
    const navigation = history.navigation || {};
    const performance = history.performance || {};

    return (
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Card title="Navigation Overview" size="small">
          <Descriptions bordered size="small">
            <Descriptions.Item label="Visited Domains">
              {visitedDomains.length}
            </Descriptions.Item>
            <Descriptions.Item label="Referrer">
              {history.referrer || 'None'}
            </Descriptions.Item>
            <Descriptions.Item label="Session History Length">
              {history.sessionHistory?.length || 0}
            </Descriptions.Item>
          </Descriptions>
        </Card>

        {history.error && (
          <Alert
            message="History Capture Error"
            description={history.error}
            type="error"
            showIcon
          />
        )}

        {visitedDomains.length > 0 && (
          <Card title="Visited Domains" size="small">
            <Space wrap>
              {visitedDomains.map((domain, index) => (
                <Tag key={index} color="blue">{domain}</Tag>
              ))}
            </Space>
          </Card>
        )}

        {Object.keys(navigation).length > 0 && (
          <Card title="Navigation Information" size="small">
            <Descriptions bordered size="small">
              <Descriptions.Item label="Navigation Type">
                {getNavigationType(navigation.type)}
              </Descriptions.Item>
              <Descriptions.Item label="Redirect Count">
                {navigation.redirectCount || 0}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        )}

        {Object.keys(performance).length > 0 && (
          <Card title="Performance Information" size="small">
            <Descriptions bordered size="small">
              <Descriptions.Item label="Page Load Time">
                {performance.loadEventEnd ? `${Math.round(performance.loadEventEnd)}ms` : 'N/A'}
              </Descriptions.Item>
              <Descriptions.Item label="DOM Content Loaded">
                {performance.domContentLoadedEventEnd ? `${Math.round(performance.domContentLoadedEventEnd)}ms` : 'N/A'}
              </Descriptions.Item>
              <Descriptions.Item label="Total Duration">
                {performance.duration ? `${Math.round(performance.duration)}ms` : 'N/A'}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        )}
      </Space>
    );
  };

  const renderCookiesTab = () => {
    const cookies = profileData.cookies || [];
    
    const columns = [
      {
        title: 'Name',
        dataIndex: 'name',
        key: 'name',
        width: '20%',
        ellipsis: true,
      },
      {
        title: 'Domain',
        dataIndex: 'domain',
        key: 'domain',
        width: '20%',
        ellipsis: true,
      },
      {
        title: 'Value',
        dataIndex: 'value',
        key: 'value',
        ellipsis: true,
        render: (value) => (
          <Text code style={{ maxWidth: 200 }}>
            {value && value.length > 50 ? `${value.substring(0, 50)}...` : value}
          </Text>
        )
      },
      {
        title: 'Secure',
        dataIndex: 'secure',
        key: 'secure',
        width: '10%',
        render: (secure) => secure ? <Tag color="green">Yes</Tag> : <Tag>No</Tag>
      },
      {
        title: 'HttpOnly',
        dataIndex: 'httpOnly',
        key: 'httpOnly',
        width: '10%',
        render: (httpOnly) => httpOnly ? <Tag color="green">Yes</Tag> : <Tag>No</Tag>
      },
      {
        title: 'SameSite',
        dataIndex: 'sameSite',
        key: 'sameSite',
        width: '10%',
        render: (sameSite) => <Tag color="blue">{sameSite || 'None'}</Tag>
      }
    ];

    return (
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Card title="Cookies Overview" size="small">
          <Descriptions bordered size="small">
            <Descriptions.Item label="Total Cookies">
              {cookies.length}
            </Descriptions.Item>
            <Descriptions.Item label="Unique Domains">
              {new Set(cookies.map(c => c.domain)).size}
            </Descriptions.Item>
            <Descriptions.Item label="Secure Cookies">
              {cookies.filter(c => c.secure).length}
            </Descriptions.Item>
          </Descriptions>
        </Card>

        <Card title="Cookies List" size="small">
          <Table
            columns={columns}
            dataSource={cookies.map((cookie, index) => ({ ...cookie, key: index }))}
            pagination={{ pageSize: 10 }}
            size="small"
            scroll={{ x: true }}
          />
        </Card>
      </Space>
    );
  };

  // Helper functions
  const formatBytes = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getNavigationType = (type) => {
    const types = {
      0: 'Navigate',
      1: 'Reload',
      2: 'Back/Forward',
      255: 'Reserved'
    };
    return types[type] || 'Unknown';
  };

  return (
    <div style={{ padding: '16px' }}>
      <Tabs activeKey={activeTab} onChange={setActiveTab} size="large">
        <TabPane 
          tab={
            <span>
              <InfoCircleOutlined />
              Summary
            </span>
          } 
          key="summary"
        >
          {renderSummaryTab()}
        </TabPane>
        
        <TabPane 
          tab={
            <span>
              <DatabaseOutlined />
              LocalStorage ({profileData.localStorage?.length || 0})
            </span>
          } 
          key="localStorage"
        >
          {renderLocalStorageTab()}
        </TabPane>
        
        <TabPane 
          tab={
            <span>
              <DatabaseOutlined />
              IndexedDB ({(profileData.indexedDB?.databases || []).length})
            </span>
          } 
          key="indexedDB"
        >
          {renderIndexedDBTab()}
        </TabPane>
        
        <TabPane 
          tab={
            <span>
              <HistoryOutlined />
              History ({(profileData.history?.visitedDomains || []).length} domains)
            </span>
          } 
          key="history"
        >
          {renderHistoryTab()}
        </TabPane>
        
        <TabPane 
          tab={
            <span>
              <SettingOutlined />
              Cookies ({(profileData.cookies || []).length})
            </span>
          } 
          key="cookies"
        >
          {renderCookiesTab()}
        </TabPane>
      </Tabs>
    </div>
  );
};

export default EnhancedDataDisplay;
