/**
 * ProfileForm Component - Form for creating/editing profiles
 */

import React, { useState, useEffect } from 'react';
import { Form, Input, Select, Button, message, Modal, Space } from 'antd';
import { PlusOutlined, EditOutlined } from '@ant-design/icons';
import { apiService } from '../../services/api';

const { TextArea } = Input;
const { Option } = Select;

const ProfileForm = ({ 
  visible, 
  onCancel, 
  onSuccess, 
  editingProfile = null,
  profileGroups = [],
  accounts = [],
  loading = false 
}) => {
  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    if (visible) {
      if (editingProfile) {
        form.setFieldsValue({
          name: editingProfile.name,
          account_id: editingProfile.account_id,
          profile_group_id: editingProfile.profile_group_id,
          description: editingProfile.description,
        });
      } else {
        form.resetFields();
      }
    }
  }, [visible, editingProfile, form]);

  const handleSubmit = async (values) => {
    setSubmitting(true);
    try {
      let response;
      if (editingProfile) {
        response = await apiService.updateProfileItem(editingProfile.id, values);
      } else {
        response = await apiService.createProfileItem(values);
      }

      message.success(
        editingProfile
          ? 'Profile updated successfully!'
          : 'Profile created successfully!'
      );
      form.resetFields();
      onSuccess(response.data);
    } catch (error) {
      console.error('Profile operation error:', error);
      message.error(error.response?.data?.message || error.message || 'Operation failed');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Modal
      title={
        <Space>
          {editingProfile ? <EditOutlined /> : <PlusOutlined />}
          {editingProfile ? 'Edit Profile' : 'Create New Profile'}
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={600}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        size="large"
      >
        <Form.Item
          name="name"
          label="Profile Name"
          rules={[
            { required: true, message: 'Please enter profile name!' },
            { max: 255, message: 'Profile name must be less than 255 characters!' },
          ]}
        >
          <Input 
            placeholder="Enter profile name (e.g., profile1, profile2)"
            showCount
            maxLength={255}
          />
        </Form.Item>

        <Form.Item
          name="profile_group_id"
          label="Profile Group"
          rules={[
            { required: true, message: 'Please select a profile group!' },
          ]}
        >
          <Select
            placeholder="Select profile group"
            showSearch
            optionFilterProp="children"
          >
            {profileGroups.map(group => (
              <Option key={group.id} value={group.id}>
                {group.name}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="account_id"
          label="Account"
          rules={[
            { required: true, message: 'Please select an account!' },
          ]}
        >
          <Select
            placeholder="Select account"
            showSearch
            optionFilterProp="children"
          >
            {accounts.map(account => (
              <Option key={account.id} value={account.id}>
                {account.browser_profile_id || `Account ${account.id}`}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="description"
          label="Description"
          rules={[
            { max: 1000, message: 'Description must be less than 1000 characters!' },
          ]}
        >
          <TextArea
            placeholder="Enter profile description (optional)"
            rows={3}
            showCount
            maxLength={1000}
          />
        </Form.Item>

        <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
          <Space>
            <Button onClick={onCancel}>
              Cancel
            </Button>
            <Button 
              type="primary" 
              htmlType="submit" 
              loading={submitting}
              icon={editingProfile ? <EditOutlined /> : <PlusOutlined />}
            >
              {editingProfile ? 'Update Profile' : 'Create Profile'}
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ProfileForm;
