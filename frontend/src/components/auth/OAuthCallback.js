/**
 * OAuth Callback Component
 * Handles OAuth callback from auto-login service with Bearer tokens
 */

import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Spin, Alert } from 'antd';
import { useAuth } from '../../contexts/AuthContext';

const OAuthCallback = () => {
  const navigate = useNavigate();
  const { handleOAuthCallback } = useAuth();
  const [status, setStatus] = useState('processing');
  const [error, setError] = useState(null);

  useEffect(() => {
    const processCallback = async () => {
      try {
        console.log('🔍 [OAUTH_CALLBACK] Processing OAuth callback...');
        
        // Get tokens from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const accessToken = urlParams.get('access_token');
        const refreshToken = urlParams.get('refresh_token');

        console.log('🔍 [OAUTH_CALLBACK] Tokens found:', {
          hasAccessToken: !!accessToken,
          hasRefreshToken: !!refreshToken
        });

        if (!accessToken) {
          throw new Error('No access token found in callback URL');
        }

        // Process the callback
        const success = await handleOAuthCallback(accessToken, refreshToken);

        if (success) {
          console.log('✅ [OAUTH_CALLBACK] OAuth callback processed successfully');
          setStatus('success');
          
          // Redirect to dashboard after a short delay
          setTimeout(() => {
            navigate('/dashboard', { replace: true });
          }, 1500);
        } else {
          throw new Error('Failed to process OAuth callback');
        }

      } catch (error) {
        console.error('❌ [OAUTH_CALLBACK] OAuth callback failed:', error);
        setStatus('error');
        setError(error.message);
        
        // Redirect to login after error display
        setTimeout(() => {
          navigate('/login', { replace: true });
        }, 3000);
      }
    };

    processCallback();
  }, [handleOAuthCallback, navigate]);

  const renderContent = () => {
    switch (status) {
      case 'processing':
        return (
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <Spin size="large" />
            <div style={{ marginTop: '20px', fontSize: '16px' }}>
              Processing authentication...
            </div>
          </div>
        );

      case 'success':
        return (
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <Alert
              message="Authentication Successful"
              description="You have been successfully authenticated. Redirecting to dashboard..."
              type="success"
              showIcon
            />
          </div>
        );

      case 'error':
        return (
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <Alert
              message="Authentication Failed"
              description={error || 'An error occurred during authentication. Redirecting to login...'}
              type="error"
              showIcon
            />
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div style={{ 
      minHeight: '100vh', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      backgroundColor: '#f5f5f5'
    }}>
      <div style={{
        backgroundColor: 'white',
        padding: '40px',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        minWidth: '400px'
      }}>
        {renderContent()}
      </div>
    </div>
  );
};

export default OAuthCallback;
