/**
 * Error Boundary Component - Catches React errors and displays debug info
 */

import React from 'react';
import { Card, Typography, Button, Space, Alert } from 'antd';
import { ExclamationCircleOutlined, ReloadOutlined } from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log error details
    console.error('❌ [ERROR BOUNDARY] React error caught:', error);
    console.error('❌ [ERROR BOUNDARY] Error info:', errorInfo);
    
    this.setState({
      error: error,
      errorInfo: errorInfo
    });
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{ 
          padding: '20px',
          minHeight: '100vh',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <Card 
            style={{ 
              width: '90%', 
              maxWidth: 800,
              borderRadius: 16,
              boxShadow: '0 20px 40px rgba(0,0,0,0.1)'
            }}
          >
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              <div style={{ textAlign: 'center' }}>
                <ExclamationCircleOutlined style={{ fontSize: 48, color: '#ff4d4f' }} />
                <Title level={2} style={{ margin: '16px 0', color: '#ff4d4f' }}>
                  Application Error
                </Title>
              </div>

              <Alert
                message="React Component Error"
                description="The application encountered an error and cannot continue. Please check the console for details."
                type="error"
                showIcon
              />

              {this.state.error && (
                <div>
                  <Title level={4}>Error Details:</Title>
                  <Paragraph code style={{ background: '#f5f5f5', padding: '12px', borderRadius: '4px' }}>
                    {this.state.error.toString()}
                  </Paragraph>
                </div>
              )}

              {this.state.errorInfo && (
                <div>
                  <Title level={4}>Component Stack:</Title>
                  <Paragraph code style={{ 
                    background: '#f5f5f5', 
                    padding: '12px', 
                    borderRadius: '4px',
                    maxHeight: '200px',
                    overflow: 'auto',
                    fontSize: '12px'
                  }}>
                    {this.state.errorInfo.componentStack}
                  </Paragraph>
                </div>
              )}

              <div style={{ textAlign: 'center' }}>
                <Space>
                  <Button 
                    type="primary" 
                    icon={<ReloadOutlined />}
                    onClick={() => window.location.reload()}
                  >
                    Reload Application
                  </Button>
                  <Button 
                    onClick={() => {
                      console.log('Opening DevTools...');
                      if (window.electronAPI) {
                        // In Electron, DevTools should already be open
                        console.log('Running in Electron - DevTools should be visible');
                      }
                    }}
                  >
                    Open Console
                  </Button>
                </Space>
              </div>

              <Alert
                message="Debug Information"
                description={
                  <div>
                    <Text strong>Environment:</Text> {process.env.NODE_ENV || 'development'}<br/>
                    <Text strong>Electron:</Text> {window.electronAPI ? 'Yes' : 'No'}<br/>
                    <Text strong>Timestamp:</Text> {new Date().toISOString()}
                  </div>
                }
                type="info"
              />
            </Space>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
