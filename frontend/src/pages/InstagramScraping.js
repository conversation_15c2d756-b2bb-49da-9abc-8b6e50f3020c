/**
 * Instagram Scraping Page Component - Complete Implementation
 */

import React, { useState, useEffect } from 'react';
import {
  Card, Form, Input, Select, Button, Space, Table, Progress,
  message, Row, Col, InputNumber, Checkbox, Tag, Modal, Tooltip,
  Alert, Statistic
} from 'antd';
import {
  SearchOutlined, PlayCircleOutlined, StopOutlined, EyeOutlined,
  DownloadOutlined, ReloadOutlined, UserOutlined, CheckCircleOutlined,
  CloseCircleOutlined, ExportOutlined, InstagramOutlined
} from '@ant-design/icons';
import { apiService } from '../services/api';
import { useAuth } from '../contexts/AuthContext';

const { Option } = Select;

const InstagramScraping = () => {
  const { user } = useAuth();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [profiles, setProfiles] = useState([]);
  const [tasks, setTasks] = useState([]);
  const [activeTask, setActiveTask] = useState(null);
  const [exportHistory, setExportHistory] = useState([]);
  const [selectedTask, setSelectedTask] = useState(null);
  const [resultsModalVisible, setResultsModalVisible] = useState(false);
  const [taskResults, setTaskResults] = useState([]);
  const [exportModalVisible, setExportModalVisible] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);

  // Polling interval for active tasks
  const POLLING_INTERVAL = 5500;

  useEffect(() => {
    loadInitialData();
  }, []);

  useEffect(() => {
    let interval;
    if (activeTask) {
      interval = setInterval(() => {
        pollTaskStatus(activeTask);
      }, POLLING_INTERVAL);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [activeTask]);

  const loadInitialData = async () => {
    try {
      setLoading(true);

      // Load profiles, tasks, and export history with better error handling
      const [profilesResponse, tasksData, exportsData] = await Promise.all([
        apiService.getProfiles().catch((error) => {
          console.error('Failed to load profiles:', error);
          message.error('Failed to load profiles: ' + error.message);
          return [];
        }),
        apiService.get('/api/instagram-scraping/').catch((error) => {
          console.error('Failed to load Instagram scraping tasks:', error);
          message.error('Failed to load Instagram scraping tasks: ' + error.message);
          return [];
        }),
        apiService.get('/api/instagram-scraping/exports/history').catch((error) => {
          console.warn('Failed to load export history:', error);
          return { exports: [] };
        })
      ]);

      console.log('✅ InstagramScraping: Loaded profiles:', profilesResponse.data);

      const instagramProfiles = Array.isArray(profilesResponse.data)
        ? profilesResponse.data.filter(profile =>
            profile.instagram_logged_in && profile.type === 'instagram'
          )
        : [];

      setProfiles(instagramProfiles);
      setTasks(Array.isArray(tasksData.data) ? tasksData.data : []);
      setExportHistory(Array.isArray(exportsData.exports) ? exportsData.exports : []);

      // Check for active tasks
      const activeTasks = Array.isArray(tasksData.data) 
        ? tasksData.data.filter(task => ['running', 'pending'].includes(task.status))
        : [];
      
      if (activeTasks.length > 0) {
        setActiveTask(activeTasks[0].task_id);
      }

    } catch (error) {
      console.error('Failed to load initial data:', error);
      message.error('Failed to load initial data: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const pollTaskStatus = async (taskId) => {
    try {
      console.log(`🔄 [Polling] Checking status for task ${taskId}`);
      const status = await apiService.getInstagramScrapingStatus(taskId);

      console.log(`📊 [Polling] Task ${taskId} status:`, status);
      console.log(`📊 [Polling] Status type:`, typeof status);
      console.log(`📊 [Polling] Status keys:`, Object.keys(status || {}));
      console.log(`📊 [Polling] Status.status:`, status?.status);

      // Update tasks list with current status
      setTasks(prevTasks =>
        prevTasks.map(task =>
          task.task_id === taskId
            ? { ...task, ...status }
            : task
        )
      );

      if (['completed', 'failed', 'stopped'].includes(status.status)) {
        console.log(`✅ [Polling] Task ${taskId} finished with status: ${status.status}`);
        setActiveTask(null);

        if (status.status === 'completed') {
          message.success(`Instagram scraping completed! Found ${status.total_scraped || 0} users.`);
        } else if (status.status === 'failed') {
          message.error(`Instagram scraping failed: ${status.error || 'Unknown error'}`);
        } else if (status.status === 'stopped') {
          message.info('Instagram scraping was stopped');
        }

        // Reload data to get latest statistics
        console.log(`🔄 [Polling] Reloading initial data after task completion`);
        loadInitialData();
      } else {
        console.log(`⏳ [Polling] Task ${taskId} still ${status.status}, continuing polling`);
      }

    } catch (error) {
      console.error(`❌ [Polling] Failed to poll task status for ${taskId}:`, error);
    }
  };

  const handleStartScraping = async (values) => {
    try {
      setLoading(true);

      const config = {
        target_url: values.target_url,
        scraping_type: values.scraping_type || 'following',
        max_results: values.max_results || 1000,
        profile_id: values.profile_id
      };

      const result = await apiService.startInstagramScraping({ config });

      setActiveTask(result.task_id);
      message.success('Instagram scraping task started successfully');

      // Reset form and reload tasks
      form.resetFields();
      loadInitialData();

    } catch (error) {
      message.error('Failed to start Instagram scraping: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleStopTask = async (taskId) => {
    try {
      await apiService.stopInstagramScraping(taskId);
      message.success('Task stopped successfully');
      setActiveTask(null);
      loadInitialData();
    } catch (error) {
      message.error('Failed to stop task: ' + error.message);
    }
  };

  const handleViewResults = async (task) => {
    try {
      setSelectedTask(task);
      const results = await apiService.getInstagramScrapingResults(task.task_id);
      setTaskResults(results.results || []);
      setResultsModalVisible(true);
    } catch (error) {
      message.error('Failed to load results: ' + error.message);
    }
  };

  const handleExportResults = async (task) => {
    try {
      setExportLoading(true);
      console.log('🔄 [Export] Starting export for task:', task.task_id);
      message.loading('Exporting to Excel...', 0);

      const response = await apiService.exportInstagramScrapingResults(task.task_id, 'excel');
      message.destroy(); // Clear loading message

      console.log('📊 [Export] Export response:', response);
      console.log('📊 [Export] Response type:', typeof response);
      console.log('📊 [Export] Response keys:', Object.keys(response || {}));

      // Handle different response structures
      let result = response;
      if (response.data) {
        console.log('📊 [Export] Found response.data, checking structure...');
        if (response.data.data && typeof response.data.data === 'object') {
          console.log('📊 [Export] Found response.data.data (double-wrapped), using it as result');
          result = response.data.data;
        } else {
          console.log('📊 [Export] Using response.data as result');
          result = response.data;
        }
      }

      console.log('📊 [Export] Final result:', result);

      if (result.download_url && result.filename) {
        console.log('✅ [Export] Export successful, processing download...');

        const filename = result.filename;
        const recordCount = result.record_count || 0;
        let downloadUrl = result.download_url;

        // Create authenticated download function (same as Scraping.js)
        const authenticatedDownload = async (url) => {
          try {
            console.log('🔗 Starting authenticated download:', url);

            // Extract filename from URL or use the provided filename
            const downloadFilename = filename || url.split('/').pop() || 'instagram_export.xlsx';
            console.log('📁 Download filename:', downloadFilename);

            // Get token from API client headers
            const authHeader = apiService.client.defaults.headers.common['Authorization'];
            const token = authHeader ? authHeader.replace('Bearer ', '') : localStorage.getItem('token');
            console.log('🔑 Token available:', !!token);

            // Ensure URL is properly formatted
            let downloadUrl = url;
            if (url.startsWith(window.location.origin)) {
              downloadUrl = url.replace(window.location.origin, '');
            }
            if (!downloadUrl.startsWith('/')) {
              downloadUrl = '/' + downloadUrl;
            }

            console.log('🔗 Final download URL:', downloadUrl);

            // Use axios with proper blob handling
            const axiosResponse = await apiService.client.get(downloadUrl, {
              responseType: 'arraybuffer',
              headers: {
                'Authorization': `Bearer ${token}`,
                'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
              }
            });

            console.log('✅ Axios request successful');
            console.log('📊 Response data type:', typeof axiosResponse.data);

            // Create blob from arraybuffer
            const blob = new Blob([axiosResponse.data], {
              type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            });

            console.log('📊 Blob created:', blob.size, 'bytes');

            // Create blob URL
            const blobUrl = window.URL.createObjectURL(blob);
            console.log('🔗 Blob URL created:', blobUrl);

            // Create temporary link and click it
            const link = document.createElement('a');
            link.href = blobUrl;
            link.download = downloadFilename;
            link.style.display = 'none';

            document.body.appendChild(link);
            console.log('🖱️ Triggering download click');
            link.click();
            document.body.removeChild(link);

            // Clean up blob URL after a delay
            setTimeout(() => {
              window.URL.revokeObjectURL(blobUrl);
              console.log('🧹 Blob URL cleaned up');
            }, 1000);

            console.log('✅ File download initiated successfully');
            message.success(`File "${downloadFilename}" download started`);

          } catch (downloadError) {
            console.error('❌ Download failed:', downloadError);
            const errorMessage = downloadError.response?.data?.message ||
                               downloadError.response?.statusText ||
                               downloadError.message ||
                               'Unknown download error';
            message.error('Download failed: ' + errorMessage);
          }
        };

        // Show success message with download button
        message.success(
          <div>
            <div>Excel export completed successfully</div>
            <div>File: {filename}</div>
            <div>Records: {recordCount}</div>
            <Button
              type="link"
              size="small"
              onClick={() => authenticatedDownload(downloadUrl)}
            >
              Download Now
            </Button>
          </div>,
          10 // Show for 10 seconds
        );

        // Auto download
        setTimeout(() => {
          console.log('🔗 [Export] Triggering auto-download');
          authenticatedDownload(downloadUrl);
        }, 1000);

        loadInitialData(); // Reload to update export history
      } else {
        console.error('❌ Export failed - no download URL or filename:', result);
        message.error('Export failed: No download URL received');
      }
    } catch (error) {
      message.destroy();
      console.error('❌ [Export] Export failed:', error);
      message.error('Failed to export results: ' + (error.response?.data?.message || error.message));
    } finally {
      setExportLoading(false);
    }
  };

  // Table columns for tasks
  const taskColumns = [
    {
      title: 'Task ID',
      dataIndex: 'task_id',
      key: 'task_id',
      width: 120,
      render: (text) => (
        <Tooltip title={text}>
          <code style={{ fontSize: '12px' }}>
            {text ? text.substring(0, 8) + '...' : 'N/A'}
          </code>
        </Tooltip>
      ),
    },
    {
      title: 'Type',
      dataIndex: 'scraping_type',
      key: 'scraping_type',
      width: 100,
      render: (type) => (
        <Tag color={type === 'following' ? 'blue' : 'green'}>
          {type === 'following' ? 'Following' : 'Post'}
        </Tag>
      ),
    },
    {
      title: 'Target URL',
      dataIndex: 'target_url',
      key: 'target_url',
      width: 200,
      render: (url) => (
        <Tooltip title={url}>
          <a href={url} target="_blank" rel="noopener noreferrer" style={{ fontSize: '12px' }}>
            {url ? url.substring(0, 30) + '...' : 'N/A'}
          </a>
        </Tooltip>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status) => {
        const statusConfig = {
          pending: { color: 'orange', icon: <CloseCircleOutlined /> },
          running: { color: 'blue', icon: <PlayCircleOutlined /> },
          completed: { color: 'green', icon: <CheckCircleOutlined /> },
          failed: { color: 'red', icon: <CloseCircleOutlined /> },
          stopped: { color: 'default', icon: <StopOutlined /> }
        };
        
        const config = statusConfig[status] || statusConfig.pending;
        
        return (
          <Tag color={config.color} icon={config.icon}>
            {status ? status.toUpperCase() : 'UNKNOWN'}
          </Tag>
        );
      },
    },
    {
      title: 'Progress',
      dataIndex: 'progress',
      key: 'progress',
      width: 150,
      render: (progress, record) => (
        <div>
          <Progress 
            percent={Math.round(progress || 0)} 
            size="small" 
            status={record.status === 'failed' ? 'exception' : 'normal'}
          />
          <div style={{ fontSize: '11px', color: '#666', marginTop: '2px' }}>
            {record.total_scraped || 0} / {record.total_found || 0} users
          </div>
        </div>
      ),
    },
    {
      title: 'Created',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120,
      render: (date) => date ? new Date(date).toLocaleDateString() : 'N/A',
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          {record.status === 'running' && (
            <Button
              size="small"
              danger
              icon={<StopOutlined />}
              onClick={() => handleStopTask(record.task_id)}
            >
              Stop
            </Button>
          )}
          
          {record.status === 'completed' && (
            <>
              <Button
                size="small"
                icon={<EyeOutlined />}
                onClick={() => handleViewResults(record)}
              >
                View
              </Button>
              <Button
                size="small"
                type="primary"
                icon={<DownloadOutlined />}
                onClick={() => handleExportResults(record)}
                loading={exportLoading}
              >
                Export
              </Button>
            </>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <h1><InstagramOutlined /> Instagram Scraping</h1>
        </Col>
        <Col>
          <Button
            icon={<ReloadOutlined />}
            onClick={loadInitialData}
            loading={loading}
          >
            Refresh
          </Button>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* Create New Task */}
        <Col xs={24} lg={12}>
          <Card title="Create Instagram Scraping Task">
            <Form
              form={form}
              layout="vertical"
              onFinish={handleStartScraping}
            >
              <Form.Item
                name="target_url"
                label="Instagram URL"
                rules={[
                  { required: true, message: 'Please enter Instagram URL' },
                  { type: 'url', message: 'Please enter a valid URL' }
                ]}
              >
                <Input placeholder="https://www.instagram.com/username/ or https://www.instagram.com/p/post_id/" />
              </Form.Item>

              <Form.Item
                name="scraping_type"
                label="Type"
                rules={[{ required: true, message: 'Please select scraping type' }]}
                initialValue="following"
              >
                <Select placeholder="Select scraping type">
                  <Option value="following">Following</Option>
                  <Option value="post">Post</Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="profile_id"
                label="Profile to Use"
                rules={[{ required: true, message: 'Please select a profile' }]}
              >
                <Select 
                  placeholder="Select Instagram profile"
                  loading={loading}
                  notFoundContent={
                    profiles.length === 0 ? (
                      <div style={{ textAlign: 'center', padding: '20px' }}>
                        <UserOutlined style={{ fontSize: '24px', color: '#ccc', marginBottom: '8px' }} />
                        <div>No Instagram profiles logged in</div>
                        <div style={{ fontSize: '12px', color: '#999' }}>
                          Please login to Instagram profiles first
                        </div>
                      </div>
                    ) : null
                  }
                >
                  {profiles.map(profile => (
                    <Option key={profile.id} value={profile.id}>
                      <Space>
                        <UserOutlined />
                        {profile.name}
                        <Tag size="small" color="green">Instagram</Tag>
                      </Space>
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="max_results"
                label="Max Results"
                initialValue={1000}
              >
                <InputNumber
                  min={1}
                  max={10000}
                  style={{ width: '100%' }}
                  placeholder="Maximum number of users to scrape"
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading || activeTask}
                  disabled={profiles.length === 0}
                  icon={<PlayCircleOutlined />}
                  block
                >
                  {activeTask ? 'Scraping in Progress...' : 'Start Instagram Scraping'}
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        {/* Statistics */}
        <Col xs={24} lg={12}>
          <Card title="Statistics">
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="Total Tasks"
                  value={tasks.length}
                  prefix={<SearchOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="Completed Tasks"
                  value={tasks.filter(t => t.status === 'completed').length}
                  prefix={<CheckCircleOutlined />}
                />
              </Col>
            </Row>
            <Row gutter={16} style={{ marginTop: 16 }}>
              <Col span={12}>
                <Statistic
                  title="Total Users Scraped"
                  value={tasks.reduce((sum, task) => sum + (task.total_scraped || 0), 0)}
                  prefix={<UserOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="Instagram Profiles"
                  value={profiles.length}
                  prefix={<InstagramOutlined />}
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* Active Task Alert */}
      {activeTask && (
        <Alert
          message="Instagram Scraping in Progress"
          description={`Task ${activeTask} is currently running. Please wait for completion.`}
          type="info"
          showIcon
          style={{ marginTop: 16, marginBottom: 16 }}
          action={
            <Button size="small" danger onClick={() => handleStopTask(activeTask)}>
              Stop Task
            </Button>
          }
        />
      )}

      {/* Tasks Table */}
      <Card title="Instagram Scraping Tasks" style={{ marginTop: 16 }}>
        <Table
          columns={taskColumns}
          dataSource={tasks}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} tasks`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* Results Modal */}
      <Modal
        title={`Results for Task ${selectedTask?.task_id}`}
        open={resultsModalVisible}
        onCancel={() => setResultsModalVisible(false)}
        width={800}
        footer={[
          <Button key="close" onClick={() => setResultsModalVisible(false)}>
            Close
          </Button>,
          <Button
            key="export"
            type="primary"
            icon={<ExportOutlined />}
            onClick={() => handleExportResults(selectedTask)}
            loading={exportLoading}
          >
            Export to Excel
          </Button>,
        ]}
      >
        <Table
          columns={[
            {
              title: 'Username',
              dataIndex: 'username',
              key: 'username',
            },
            {
              title: selectedTask?.scraping_type === 'post' ? 'Comment' : 'Profile Name',
              dataIndex: selectedTask?.scraping_type === 'post' ? 'comment' : 'profile_name',
              key: selectedTask?.scraping_type === 'post' ? 'comment' : 'profile_name',
              render: (text) => (
                <div style={{ maxWidth: '300px', wordWrap: 'break-word' }}>
                  {text}
                </div>
              ),
            },
            {
              title: 'Profile URL',
              dataIndex: 'profile_url',
              key: 'profile_url',
              render: (url) => (
                <a href={url} target="_blank" rel="noopener noreferrer">
                  View Profile
                </a>
              ),
            },
          ]}
          dataSource={taskResults}
          rowKey="id"
          pagination={{ pageSize: 10 }}
          size="small"
        />
      </Modal>
    </div>
  );
};

export default InstagramScraping;
