"""
Pytest configuration and shared fixtures for profile sharing tests
"""

import pytest
import asyncio
import os
import tempfile
import shutil
from unittest.mock import Mock, AsyncMock, patch
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient
from httpx import AsyncClient

from app.main import app
from app.core.database import Base, get_db
from app.core.config import settings
from app.models.profile import Profile
from app.services.profile_sync_service import ProfileSyncService
from app.services.profile_access_control import ProfileAccessControl
from app.services.profile_security import profile_encryption, audit_logger
from app.services.profile_performance import profile_performance_optimizer


# Test database configuration
TEST_DATABASE_URL = "sqlite+aiosqlite:///./test.db"


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    policy = asyncio.get_event_loop_policy()
    loop = policy.new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
async def test_engine():
    """Create test database engine"""
    engine = create_async_engine(
        TEST_DATABASE_URL,
        echo=False,
        future=True
    )
    
    # Create tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    yield engine
    
    # Cleanup
    await engine.dispose()
    if os.path.exists("./test.db"):
        os.remove("./test.db")


@pytest.fixture
async def db_session(test_engine):
    """Create test database session"""
    async_session = sessionmaker(
        test_engine, class_=AsyncSession, expire_on_commit=False
    )
    
    async with async_session() as session:
        yield session
        await session.rollback()


@pytest.fixture
def override_get_db(db_session):
    """Override database dependency for testing"""
    async def _override_get_db():
        yield db_session
    
    app.dependency_overrides[get_db] = _override_get_db
    yield
    app.dependency_overrides.clear()


@pytest.fixture
def test_client(override_get_db):
    """Create test client"""
    return TestClient(app)


@pytest.fixture
async def async_client(override_get_db):
    """Create async test client"""
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client


@pytest.fixture
def temp_profile_dir():
    """Create temporary directory for profile testing"""
    temp_dir = tempfile.mkdtemp(prefix="test_profile_")
    yield temp_dir
    shutil.rmtree(temp_dir, ignore_errors=True)


@pytest.fixture
def mock_redis():
    """Mock Redis client for testing"""
    with patch('redis.asyncio.from_url') as mock_redis:
        mock_client = AsyncMock()
        mock_client.ping.return_value = True
        mock_client.get.return_value = None
        mock_client.setex.return_value = True
        mock_client.delete.return_value = 1
        mock_client.keys.return_value = []
        mock_client.info.return_value = {
            'connected_clients': 1,
            'used_memory': 1024,
            'used_memory_human': '1K',
            'keyspace_hits': 10,
            'keyspace_misses': 5
        }
        
        mock_redis.return_value = mock_client
        yield mock_client


@pytest.fixture
def mock_auto_login_service():
    """Mock auto-login service HTTP calls"""
    with patch('httpx.AsyncClient') as mock_client:
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"data": {"user_id": 1}}
        
        mock_client.return_value.__aenter__.return_value.get.return_value = mock_response
        mock_client.return_value.__aenter__.return_value.post.return_value = mock_response
        
        yield mock_client


@pytest.fixture
def test_profile_data():
    """Sample profile data for testing"""
    return {
        "id": 1,
        "name": "test_profile",
        "profile_path": "/tmp/test_profile",
        "proxy_type": "no_proxy",
        "proxy_host": None,
        "proxy_port": None,
        "proxy_username": None,
        "proxy_password": None,
        "fingerprint_data": {"user_agent": "test_agent"},
        "is_shared": True,
        "account_id": 123,
        "created_at": "2024-01-01T00:00:00",
        "updated_at": "2024-01-01T00:00:00"
    }


@pytest.fixture
def test_user_data():
    """Sample user data for testing"""
    return {
        "id": 1,
        "username": "test_user",
        "role": "user",
        "email": "<EMAIL>"
    }


@pytest.fixture
def test_admin_data():
    """Sample admin data for testing"""
    return {
        "id": 2,
        "username": "admin_user",
        "role": "admin",
        "email": "<EMAIL>"
    }


@pytest.fixture
def mock_auth_user(test_user_data):
    """Mock authenticated user"""
    with patch('app.middleware.auth.require_auth') as mock_auth:
        mock_user = Mock()
        mock_user.id = test_user_data["id"]
        mock_user.username = test_user_data["username"]
        mock_user.role = test_user_data["role"]
        mock_user.email = test_user_data["email"]
        
        mock_auth.return_value = mock_user
        yield mock_user


@pytest.fixture
def mock_auth_admin(test_admin_data):
    """Mock authenticated admin"""
    with patch('app.middleware.auth.require_admin') as mock_auth:
        mock_admin = Mock()
        mock_admin.id = test_admin_data["id"]
        mock_admin.username = test_admin_data["username"]
        mock_admin.role = test_admin_data["role"]
        mock_admin.email = test_admin_data["email"]
        
        mock_auth.return_value = mock_admin
        yield mock_admin


@pytest.fixture
def mock_profile_sync_service():
    """Mock profile sync service"""
    with patch('app.services.profile_sync_service.ProfileSyncService') as mock_service:
        mock_instance = Mock()
        mock_instance.capture_profile_data.return_value = {
            "success": True,
            "items_captured": 10
        }
        mock_instance.restore_profile_data.return_value = {
            "success": True,
            "items_restored": 8
        }
        mock_instance.sync_profile_incremental.return_value = {
            "success": True,
            "items_synced": 5
        }
        
        mock_service.return_value = mock_instance
        yield mock_instance


@pytest.fixture
def mock_profile_access_control():
    """Mock profile access control"""
    with patch('app.services.profile_access_control.ProfileAccessControl') as mock_service:
        mock_instance = Mock()
        mock_instance.check_profile_access.return_value = {
            "hasAccess": True,
            "accessType": "owner",
            "permissions": {
                "can_view_profile": True,
                "can_modify_profile": True,
                "can_export_data": True
            }
        }
        mock_instance.get_user_accessible_profiles.return_value = []
        
        mock_service.return_value = mock_instance
        yield mock_instance


@pytest.fixture
def mock_camoufox_manager():
    """Mock Camoufox browser manager"""
    with patch('app.services.camoufox_manager.CamoufoxBrowserManager') as mock_manager:
        mock_instance = Mock()
        mock_instance.launch_browser_with_sync.return_value = {
            "success": True,
            "browser_instance": Mock(),
            "sync_enabled": True,
            "monitoring_active": True
        }
        mock_instance.close_browser_with_sync.return_value = {
            "success": True,
            "sync_result": {"items_synced": 5},
            "sync_enabled": True
        }
        mock_instance.capture_live_profile_data.return_value = {
            "success": True,
            "data": {"test": "data"},
            "timestamp": "2024-01-01T00:00:00"
        }
        
        mock_manager.return_value = mock_instance
        yield mock_instance


@pytest.fixture
def sample_profile_storage_data():
    """Sample profile storage data for testing"""
    return {
        "local_storage": {
            "facebook.com": {
                "user_id": "123456789",
                "session_token": "abc123def456"
            }
        },
        "cookies": [
            {
                "name": "c_user",
                "value": "123456789",
                "domain": ".facebook.com",
                "path": "/",
                "secure": True
            }
        ],
        "indexeddb": {
            "facebook_db": {
                "user_data": {"name": "Test User"}
            }
        },
        "browser_history": [
            {
                "url": "https://facebook.com/profile",
                "title": "Facebook Profile",
                "visit_time": "2024-01-01T00:00:00"
            }
        ]
    }


@pytest.fixture
def mock_file_operations():
    """Mock file system operations"""
    with patch('os.path.exists', return_value=True), \
         patch('os.makedirs'), \
         patch('shutil.copytree'), \
         patch('builtins.open', create=True) as mock_open:
        
        mock_open.return_value.__enter__.return_value.read.return_value = '{"test": "data"}'
        mock_open.return_value.__enter__.return_value.write.return_value = None
        
        yield mock_open


@pytest.fixture(autouse=True)
async def setup_test_environment():
    """Setup test environment before each test"""
    # Reset any global state
    if hasattr(profile_performance_optimizer, 'cache'):
        profile_performance_optimizer.cache.redis_client = None
    
    # Clear any cached data
    yield
    
    # Cleanup after test
    pass


# Test markers
def pytest_configure(config):
    """Configure pytest markers"""
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "e2e: mark test as an end-to-end test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "security: mark test as security-related"
    )
    config.addinivalue_line(
        "markers", "performance: mark test as performance-related"
    )


# Test collection hooks
def pytest_collection_modifyitems(config, items):
    """Modify test collection"""
    for item in items:
        # Add timeout marker to all async tests
        if asyncio.iscoroutinefunction(item.function):
            item.add_marker(pytest.mark.timeout(30))
        
        # Add markers based on test name patterns
        if "test_security" in item.name or "test_encrypt" in item.name:
            item.add_marker(pytest.mark.security)
        
        if "test_performance" in item.name or "test_cache" in item.name:
            item.add_marker(pytest.mark.performance)
        
        if "test_integration" in item.name or "test_complete" in item.name:
            item.add_marker(pytest.mark.integration)


# Utility functions for tests
def create_mock_profile(**kwargs):
    """Create a mock profile object"""
    default_data = {
        "id": 1,
        "name": "test_profile",
        "profile_path": "/tmp/test_profile",
        "proxy_type": "no_proxy",
        "is_shared": True,
        "account_id": 123
    }
    default_data.update(kwargs)
    
    mock_profile = Mock()
    for key, value in default_data.items():
        setattr(mock_profile, key, value)
    
    return mock_profile


def create_mock_user(**kwargs):
    """Create a mock user object"""
    default_data = {
        "id": 1,
        "username": "test_user",
        "role": "user",
        "email": "<EMAIL>"
    }
    default_data.update(kwargs)
    
    mock_user = Mock()
    for key, value in default_data.items():
        setattr(mock_user, key, value)
    
    return mock_user


async def wait_for_async_tasks(timeout=5.0):
    """Wait for all pending async tasks to complete"""
    try:
        await asyncio.wait_for(
            asyncio.gather(*asyncio.all_tasks(), return_exceptions=True),
            timeout=timeout
        )
    except asyncio.TimeoutError:
        pass  # Some tasks might be long-running background tasks
