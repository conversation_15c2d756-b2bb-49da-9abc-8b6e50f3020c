#!/usr/bin/env python3
"""
Database corruption fix script
This script will backup the corrupted database and create a fresh one
"""

import os
import shutil
import sqlite3
import asyncio
from datetime import datetime
from pathlib import Path

# Add the project root to Python path
import sys
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.database import init_db, engine
from app.core.logger import setup_logger

logger = setup_logger(__name__)


def backup_corrupted_database():
    """Backup the corrupted database"""
    db_path = "facebook_automation.db"
    
    if not os.path.exists(db_path):
        logger.info("No database file found to backup")
        return None
    
    # Create backup with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{db_path}.corrupted_backup_{timestamp}"
    
    try:
        shutil.copy2(db_path, backup_path)
        logger.info(f"Corrupted database backed up to: {backup_path}")
        return backup_path
    except Exception as e:
        logger.error(f"Failed to backup database: {e}")
        return None


def remove_corrupted_files():
    """Remove corrupted database files"""
    files_to_remove = [
        "facebook_automation.db",
        "facebook_automation.db-shm", 
        "facebook_automation.db-wal"
    ]
    
    for file_path in files_to_remove:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                logger.info(f"Removed corrupted file: {file_path}")
            except Exception as e:
                logger.error(f"Failed to remove {file_path}: {e}")


def test_database_integrity():
    """Test the new database integrity"""
    try:
        conn = sqlite3.connect("facebook_automation.db")
        cursor = conn.cursor()
        
        # Run integrity check
        cursor.execute("PRAGMA integrity_check")
        result = cursor.fetchone()
        
        if result[0] == "ok":
            logger.info("✅ Database integrity check passed")
        else:
            logger.error(f"❌ Database integrity check failed: {result[0]}")
            return False
            
        # Test basic operations
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        logger.info(f"✅ Found {len(tables)} tables in database")
        
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"Database integrity test failed: {e}")
        return False


async def main():
    """Main function to fix database corruption"""
    try:
        logger.info("🔧 Starting database corruption fix...")
        
        # Step 1: Backup corrupted database
        logger.info("📦 Backing up corrupted database...")
        backup_path = backup_corrupted_database()
        
        # Step 2: Remove corrupted files
        logger.info("🗑️ Removing corrupted database files...")
        remove_corrupted_files()
        
        # Step 3: Initialize fresh database
        logger.info("🆕 Creating fresh database...")
        await init_db()
        
        # Step 4: Test database integrity
        logger.info("🧪 Testing database integrity...")
        if test_database_integrity():
            logger.info("✅ Database corruption fix completed successfully!")
            if backup_path:
                logger.info(f"💾 Corrupted database backed up to: {backup_path}")
        else:
            logger.error("❌ Database integrity test failed after recreation")
            return False
            
        # Step 5: Test database connection
        logger.info("🔌 Testing database connection...")
        from app.core.database import AsyncSessionLocal
        async with AsyncSessionLocal() as session:
            logger.info("✅ Database connection test successful")
        
        logger.info("🎉 Database fix completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Database fix failed: {e}")
        return False
    finally:
        # Close engine
        await engine.dispose()


if __name__ == "__main__":
    success = asyncio.run(main())
    if not success:
        sys.exit(1)
