#!/usr/bin/env python3
"""
Database query script for Facebook Automation
Usage: python db_query.py
"""

import sqlite3
import json
from datetime import datetime

def connect_db():
    """Connect to the database"""
    return sqlite3.connect('facebook_automation.db')

def show_tables():
    """Show all tables in database"""
    conn = connect_db()
    cursor = conn.cursor()
    
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = cursor.fetchall()
    
    print("📊 Available Tables:")
    for table in tables:
        print(f"  - {table[0]}")
    
    conn.close()

def show_bulk_messaging_stats(limit=10):
    """Show bulk messaging statistics"""
    conn = connect_db()
    cursor = conn.cursor()
    
    query = """
    SELECT 
        task_id,
        task_name,
        status,
        total_recipients,
        messages_sent,
        messages_failed,
        success_rate,
        total_time_formatted,
        created_at,
        completed_at
    FROM bulk_messaging_statistics 
    ORDER BY created_at DESC 
    LIMIT ?
    """
    
    cursor.execute(query, (limit,))
    results = cursor.fetchall()
    
    print(f"\n📈 Bulk Messaging Statistics (Last {limit}):")
    print("-" * 120)
    print(f"{'Task ID':<12} {'Name':<15} {'Status':<10} {'Recipients':<10} {'Sent':<6} {'Failed':<6} {'Success%':<8} {'Time':<8} {'Created':<19} {'Completed':<19}")
    print("-" * 120)
    
    for row in results:
        task_id = row[0][:8] + "..." if row[0] else "N/A"
        print(f"{task_id:<12} {row[1]:<15} {row[2]:<10} {row[3]:<10} {row[4]:<6} {row[5]:<6} {row[6]:<8.1f} {row[7]:<8} {row[8][:19]:<19} {row[9][:19] if row[9] else 'N/A':<19}")
    
    conn.close()

def show_task_details(task_id):
    """Show detailed information for a specific task"""
    conn = connect_db()
    cursor = conn.cursor()
    
    query = """
    SELECT * FROM bulk_messaging_statistics 
    WHERE task_id LIKE ?
    """
    
    cursor.execute(query, (f"%{task_id}%",))
    result = cursor.fetchone()
    
    if result:
        columns = [description[0] for description in cursor.description]
        print(f"\n🔍 Task Details for {task_id}:")
        print("-" * 50)
        
        for i, column in enumerate(columns):
            value = result[i]
            if column == 'detailed_results' and value:
                try:
                    detailed = json.loads(value)
                    print(f"{column}: {json.dumps(detailed, indent=2)}")
                except:
                    print(f"{column}: {value}")
            else:
                print(f"{column}: {value}")
    else:
        print(f"❌ No task found with ID containing: {task_id}")
    
    conn.close()

def main():
    """Main function"""
    print("🗄️  Facebook Automation Database Query Tool")
    print("=" * 50)
    
    while True:
        print("\nOptions:")
        print("1. Show all tables")
        print("2. Show bulk messaging statistics")
        print("3. Show task details")
        print("4. Custom query")
        print("5. Exit")
        
        choice = input("\nEnter your choice (1-5): ").strip()
        
        if choice == '1':
            show_tables()
        
        elif choice == '2':
            limit = input("Enter limit (default 10): ").strip()
            limit = int(limit) if limit.isdigit() else 10
            show_bulk_messaging_stats(limit)
        
        elif choice == '3':
            task_id = input("Enter task ID (partial match): ").strip()
            if task_id:
                show_task_details(task_id)
            else:
                print("❌ Please enter a task ID")
        
        elif choice == '4':
            query = input("Enter SQL query: ").strip()
            if query:
                try:
                    conn = connect_db()
                    cursor = conn.cursor()
                    cursor.execute(query)
                    results = cursor.fetchall()
                    
                    print(f"\n📊 Query Results ({len(results)} rows):")
                    for row in results:
                        print(row)
                    
                    conn.close()
                except Exception as e:
                    print(f"❌ Query error: {e}")
        
        elif choice == '5':
            print("👋 Goodbye!")
            break
        
        else:
            print("❌ Invalid choice. Please try again.")

if __name__ == "__main__":
    main()
