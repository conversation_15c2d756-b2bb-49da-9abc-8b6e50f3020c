#!/usr/bin/env python3
"""
Instagram Scraping Database Migration Script
Run this script to add Instagram scraping tables to the database

Usage:
    cd backend
    source venv/bin/activate  # Activate virtual environment first
    python run_instagram_migration.py
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Check if we're in virtual environment
def check_virtual_env():
    """Check if virtual environment is activated"""
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        return True
    return False

def check_dependencies():
    """Check if required dependencies are installed"""
    try:
        import sqlalchemy
        import asyncio
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Please activate virtual environment and install dependencies:")
        print("  cd backend")
        print("  source venv/bin/activate")
        print("  pip install -r requirements.txt")
        return False

try:
    from app.core.database import engine, Base
    from app.models.instagram_scraping import InstagramScrapingTask, InstagramScrapedUser, InstagramScrapingExport
    from app.models.profile import Profile
    from app.core.logger import get_logger
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're in the backend directory and virtual environment is activated:")
    print("  cd backend")
    print("  source venv/bin/activate")
    print("  pip install -r requirements.txt")
    sys.exit(1)

logger = get_logger(__name__)

async def run_migration():
    """Run Instagram scraping database migration"""
    try:
        logger.info("🚀 Starting Instagram scraping database migration...")
        
        # Create all tables
        async with engine.begin() as conn:
            # Import all models to ensure they're registered
            from app.models import profile, instagram_scraping
            
            # Create tables
            await conn.run_sync(Base.metadata.create_all)
            
            logger.info("✅ Instagram scraping tables created successfully")
            
            # Verify tables exist
            result = await conn.execute(
                "SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'instagram_%'"
            )
            tables = result.fetchall()
            
            logger.info(f"📊 Created tables: {[table[0] for table in tables]}")
            
        logger.info("✅ Instagram scraping migration completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Migration failed: {e}")
        return False

async def verify_migration():
    """Verify that migration was successful"""
    try:
        logger.info("🔍 Verifying Instagram scraping migration...")
        
        async with engine.begin() as conn:
            # Check if tables exist
            tables_to_check = [
                'instagram_scraping_tasks',
                'instagram_scraped_users', 
                'instagram_scraping_exports'
            ]
            
            for table_name in tables_to_check:
                result = await conn.execute(
                    f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'"
                )
                table_exists = result.fetchone()
                
                if table_exists:
                    logger.info(f"✅ Table '{table_name}' exists")
                    
                    # Check table structure
                    result = await conn.execute(f"PRAGMA table_info({table_name})")
                    columns = result.fetchall()
                    logger.info(f"📋 Table '{table_name}' has {len(columns)} columns")
                else:
                    logger.error(f"❌ Table '{table_name}' does not exist")
                    return False
            
        logger.info("✅ Migration verification completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Migration verification failed: {e}")
        return False

async def main():
    """Main migration function"""
    print("=" * 60)
    print("Instagram Scraping Database Migration")
    print("=" * 60)

    # Check virtual environment
    if not check_virtual_env():
        print("⚠️  Warning: Virtual environment not detected")
        print("It's recommended to run this in the backend virtual environment:")
        print("  cd backend")
        print("  source venv/bin/activate")
        print("  python run_instagram_migration.py")
        print()

    # Check dependencies
    if not check_dependencies():
        return 1

    # Run migration
    migration_success = await run_migration()

    if migration_success:
        # Verify migration
        verification_success = await verify_migration()

        if verification_success:
            print("\n✅ Instagram scraping migration completed successfully!")
            print("\nYou can now:")
            print("1. Start the backend server with: ./scripts/start-all-services.sh")
            print("2. Use the Instagram scraping functionality")
            print("3. Create Instagram scraping tasks")
            return 0
        else:
            print("\n❌ Migration verification failed!")
            return 1
    else:
        print("\n❌ Migration failed!")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
