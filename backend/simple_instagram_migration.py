#!/usr/bin/env python3
"""
Simple Instagram Scraping Database Migration Script
This script directly creates Instagram scraping tables using SQLite
"""

import sqlite3
import os
from pathlib import Path

def create_instagram_tables():
    """Create Instagram scraping tables using direct SQLite"""
    
    # Get database path
    backend_dir = Path(__file__).parent
    db_path = backend_dir / "facebook_automation.db"
    
    print(f"📊 Using database: {db_path}")
    
    # Connect to database
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    try:
        print("🚀 Creating Instagram scraping tables...")
        
        # Create instagram_scraping_tasks table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS instagram_scraping_tasks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_id VARCHAR(255) UNIQUE NOT NULL,
                profile_id INTEGER NOT NULL,
                target_url VARCHAR(1000) NOT NULL,
                scraping_type VARCHAR(50) NOT NULL,
                max_results INTEGER DEFAULT 1000,
                status VARCHAR(50) DEFAULT 'pending',
                progress REAL DEFAULT 0.0,
                total_found INTEGER DEFAULT 0,
                total_scraped INTEGER DEFAULT 0,
                results_file_path VARCHAR(500),
                error_message TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                started_at DATETIME,
                completed_at DATETIME,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (profile_id) REFERENCES profiles (id)
            )
        """)
        
        # Create indexes for instagram_scraping_tasks
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_instagram_scraping_tasks_task_id ON instagram_scraping_tasks(task_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_instagram_scraping_tasks_profile_id ON instagram_scraping_tasks(profile_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_instagram_scraping_tasks_status ON instagram_scraping_tasks(status)")
        
        print("✅ Created instagram_scraping_tasks table")
        
        # Create instagram_scraped_users table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS instagram_scraped_users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_id VARCHAR(255) NOT NULL,
                username VARCHAR(255) NOT NULL,
                profile_name VARCHAR(500),
                profile_url VARCHAR(1000),
                user_id VARCHAR(255),
                follower_count INTEGER,
                following_count INTEGER,
                post_count INTEGER,
                is_verified VARCHAR(10),
                is_private VARCHAR(10),
                bio TEXT,
                scraped_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (task_id) REFERENCES instagram_scraping_tasks (task_id)
            )
        """)
        
        # Create indexes for instagram_scraped_users
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_instagram_scraped_users_task_id ON instagram_scraped_users(task_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_instagram_scraped_users_username ON instagram_scraped_users(username)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_instagram_scraped_users_task_username ON instagram_scraped_users(task_id, username)")
        
        print("✅ Created instagram_scraped_users table")
        
        # Create instagram_scraping_exports table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS instagram_scraping_exports (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_id VARCHAR(255) NOT NULL,
                filename VARCHAR(500) NOT NULL,
                file_path VARCHAR(1000) NOT NULL,
                file_size INTEGER,
                format VARCHAR(50) DEFAULT 'excel',
                record_count INTEGER DEFAULT 0,
                status VARCHAR(50) DEFAULT 'completed',
                error_message TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (task_id) REFERENCES instagram_scraping_tasks (task_id)
            )
        """)
        
        # Create indexes for instagram_scraping_exports
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_instagram_scraping_exports_task_id ON instagram_scraping_exports(task_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_instagram_scraping_exports_created_at ON instagram_scraping_exports(created_at)")
        
        print("✅ Created instagram_scraping_exports table")
        
        # Commit changes
        conn.commit()
        
        # Verify tables were created
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'instagram_%'")
        tables = cursor.fetchall()
        
        print(f"📊 Verified tables: {[table[0] for table in tables]}")
        
        # Show table info
        for table_name, in tables:
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            print(f"📋 Table '{table_name}' has {len(columns)} columns")
        
        print("✅ Instagram scraping migration completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        conn.rollback()
        return False
        
    finally:
        conn.close()

def main():
    """Main function"""
    print("=" * 60)
    print("Instagram Scraping Database Migration (Simple)")
    print("=" * 60)
    
    # Check if database exists
    backend_dir = Path(__file__).parent
    db_path = backend_dir / "facebook_automation.db"
    
    if not db_path.exists():
        print(f"❌ Database not found: {db_path}")
        print("Please make sure the backend has been initialized first.")
        print("You can run: python init_db.py")
        return 1
    
    # Run migration
    success = create_instagram_tables()
    
    if success:
        print("\n✅ Instagram scraping migration completed successfully!")
        print("\nYou can now:")
        print("1. Start the backend server with: ./scripts/start-all-services.sh")
        print("2. Use the Instagram scraping functionality")
        print("3. Create Instagram scraping tasks")
        return 0
    else:
        print("\n❌ Migration failed!")
        return 1

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
