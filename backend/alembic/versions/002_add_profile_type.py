"""Add profile type column

Revision ID: 002_add_profile_type
Revises: 5848e4a0670c
Create Date: 2025-08-02 10:30:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '002_add_profile_type'
down_revision = '5848e4a0670c'
branch_labels = None
depends_on = None


def upgrade():
    # Add type column to profiles table
    op.add_column('profiles', sa.Column('type', sa.String(50), nullable=False, server_default='facebook'))
    
    # Create index on type column for better query performance
    op.create_index('idx_profiles_type', 'profiles', ['type'])


def downgrade():
    # Remove index
    op.drop_index('idx_profiles_type', 'profiles')
    
    # Remove type column
    op.drop_column('profiles', 'type')
