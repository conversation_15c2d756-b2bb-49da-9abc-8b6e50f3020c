PRAGMA foreign_keys=OFF;
BEGIN TRANSACTION;
CREATE TABLE profiles (
	id INTEGER NOT NULL, 
	name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL, 
	profile_path VARCHAR(500) NOT NULL, 
	proxy_type VARCHAR(50), 
	proxy_host <PERSON><PERSON><PERSON><PERSON>(255), 
	proxy_port INTEGER, 
	proxy_username <PERSON><PERSON><PERSON><PERSON>(255), 
	proxy_password VARCHAR(255), 
	fingerprint_data JSON, 
	facebook_logged_in BOOLEAN, 
	facebook_username <PERSON><PERSON><PERSON><PERSON>(255), 
	cookies_data JSON, 
	status VARCHAR(50), 
	last_used DATETIME, 
	error_message TEXT, 
	created_at DATETIME DEFAULT CURRENT_TIMESTAMP, 
	updated_at DATETIME DEFAULT CURRENT_TIMESTAMP, account_id INTEGER, is_shared BOOLEAN, shared_by_admin_id INTEGER, sync_version INTEGER, last_sync_timestamp DATETIME, browser_data_saved BOOLEAN DEFAULT FALSE, browser_data_path VARCHAR(500), last_browser_save TIMESTAMP, session_active BOOLEAN DEFAULT FALSE, browser_pid INTEGER, browser_port INTEGER, 
	PRIMARY KEY (id)
);
INSERT INTO profiles VALUES(1,'Test Profile','/Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Test Profile_dc800a19','no_proxy',NULL,NULL,NULL,NULL,'{"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/******** Firefox/121.0", "viewport": {"width": 945, "height": 630}, "screen": {"width": 1024, "height": 768, "color_depth": 24, "pixel_depth": 24}, "language": "de-DE,de;q=0.9,en;q=0.8", "languages": ["de-DE"], "timezone": "Asia/Shanghai", "platform": "Linux x86_64", "os": "Linux", "hardware_concurrency": 16, "device_memory": 16, "webgl": {"vendor": "Mozilla", "renderer": "ANGLE (NVIDIA, NVIDIA GeForce RTX 3060 Direct3D11 vs_5_0 ps_5_0, D3D11)"}, "canvas_fingerprint": "321615d475191454", "audio_fingerprint": "2ab659dc72f03920", "fonts": ["Impact", "Arial Black", "Times New Roman", "DejaVu Sans Mono", "Tahoma", "Verdana", "Trebuchet MS", "Ubuntu", "Liberation Sans", "Segoe UI", "Cambria", "Comic Sans MS", "Ubuntu Mono", "Liberation Serif", "Lucida Sans Unicode", "Courier New", "Microsoft Sans Serif", "Liberation Mono", "Palatino Linotype", "Arial Narrow", "Georgia", "Arial", "Calibri", "DejaVu Sans", "Lucida Console", "DejaVu Serif"], "plugins": [{"name": "PDF.js", "filename": "pdf.js", "description": "Portable Document Format"}], "do_not_track": "1", "cookie_enabled": true, "online": true, "java_enabled": false, "pdf_viewer_enabled": true}',0,NULL,NULL,'active',NULL,NULL,'2025-07-10 08:34:58','2025-07-10 09:18:44',NULL,NULL,NULL,NULL,NULL,0,NULL,NULL,0,NULL,NULL);
INSERT INTO profiles VALUES(3,'test1','/Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test1_7ff3cb73','no_proxy',NULL,NULL,NULL,NULL,'{"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0", "viewport": {"width": 1903, "height": 957}, "screen": {"width": 1920, "height": 1080, "color_depth": 24, "pixel_depth": 24, "availWidth": 1920, "availHeight": 1032}, "language": "en-US", "languages": ["en-US"], "timezone": "Australia/Sydney", "platform": "Win32", "os": "Windows", "hardware_concurrency": 32, "device_memory": null, "webgl": {"vendor": "Mozilla", "renderer": "Mozilla Firefox"}, "canvas_fingerprint": "2dd8f93dc0bbc224", "audio_fingerprint": "56637b26c1b778f0", "fonts": ["Impact", "Calibri", "Verdana", "Lucida Console", "Georgia", "Cambria", "Comic Sans MS", "Arial", "Tahoma", "Arial Narrow", "Arial Black", "Palatino Linotype", "Segoe UI", "Times New Roman", "Microsoft Sans Serif", "Trebuchet MS", "Courier New", "Lucida Sans Unicode"], "plugins": [{"name": "PDF.js", "filename": "pdf.js", "description": "Portable Document Format"}], "do_not_track": "1", "cookie_enabled": true, "online": true, "java_enabled": false, "pdf_viewer_enabled": true, "browserforge_generated": true}',1,NULL,NULL,'created',NULL,NULL,'2025-07-11 16:08:06','2025-07-12 16:35:26',NULL,NULL,NULL,NULL,NULL,0,NULL,NULL,0,NULL,NULL);
INSERT INTO profiles VALUES(4,'test2','/Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test2_83d9b852','no_proxy',NULL,NULL,NULL,NULL,'{"user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:140.0) Gecko/******** Firefox/140.0", "viewport": {"width": 1429, "height": 748}, "screen": {"width": 1440, "height": 900, "color_depth": 30, "pixel_depth": 30, "availWidth": 1440, "availHeight": 900}, "language": "en-US", "languages": ["en-US"], "timezone": "Asia/Shanghai", "platform": "MacIntel", "os": "macOS", "hardware_concurrency": 12, "device_memory": null, "webgl": {"vendor": "Mozilla", "renderer": "Mozilla Firefox"}, "canvas_fingerprint": "ea8a0d51fb34701f", "audio_fingerprint": "bad9dc3cf2b20a47", "fonts": ["Cambria", "Comic Sans MS", "Impact", "Lucida Sans Unicode", "Calibri", "Verdana", "Apple Chancery", "Segoe UI", "Monaco", "Georgia", "Tahoma", "Avenir Next", "Arial Black", "Times New Roman", "Arial Narrow", "Trebuchet MS", "Helvetica", "Helvetica Neue", "Lucida Console", "Apple SD Gothic Neo", "Menlo", "Apple Color Emoji", "Arial", "Microsoft Sans Serif", "Palatino Linotype"], "plugins": [{"name": "PDF.js", "filename": "pdf.js", "description": "Portable Document Format"}], "do_not_track": "1", "cookie_enabled": true, "online": true, "java_enabled": false, "pdf_viewer_enabled": true, "browserforge_generated": true}',0,NULL,NULL,'created',NULL,NULL,'2025-07-12 16:34:26','2025-07-12 16:34:42',NULL,NULL,NULL,NULL,NULL,0,NULL,NULL,0,NULL,NULL);
INSERT INTO profiles VALUES(5,'Profile_2_1753686161448','/Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Profile_2_1753686161448_ac924f2a','no_proxy',NULL,NULL,NULL,NULL,'{"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0", "viewport": {"width": 1896, "height": 887}, "screen": {"width": 1920, "height": 1080, "color_depth": 24, "pixel_depth": 24, "availWidth": 1920, "availHeight": 1080}, "language": "en-US", "languages": ["en-US"], "timezone": "America/Los_Angeles", "platform": "Win32", "os": "Windows", "hardware_concurrency": 24, "device_memory": null, "webgl": {"vendor": "Mozilla", "renderer": "Mozilla Firefox"}, "canvas_fingerprint": "e24908568188cf52", "audio_fingerprint": "88bc496e7ace8db5", "fonts": ["Lucida Sans Unicode", "Microsoft Sans Serif", "Lucida Console", "Courier New", "Segoe UI", "Arial Black", "Impact", "Tahoma", "Cambria", "Verdana", "Comic Sans MS", "Georgia", "Arial Narrow", "Arial", "Trebuchet MS", "Calibri", "Palatino Linotype", "Times New Roman"], "plugins": [{"name": "PDF.js", "filename": "pdf.js", "description": "Portable Document Format"}], "do_not_track": "1", "cookie_enabled": true, "online": true, "java_enabled": false, "pdf_viewer_enabled": true, "browserforge_generated": true}',0,NULL,NULL,'active',NULL,NULL,'2025-07-28 07:02:41','2025-07-28 07:32:23',2,0,NULL,1,NULL,0,NULL,NULL,1,NULL,NULL);
INSERT INTO profiles VALUES(6,'Profile_4_1753687952435','/Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Profile_4_1753687952435_bd1c0118','no_proxy',NULL,NULL,NULL,NULL,'{"user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:140.0) Gecko/******** Firefox/140.0", "viewport": {"width": 1432, "height": 778}, "screen": {"width": 1440, "height": 900, "color_depth": 30, "pixel_depth": 30, "availWidth": 1440, "availHeight": 900}, "language": "en-US", "languages": ["en-US"], "timezone": "Asia/Tokyo", "platform": "MacIntel", "os": "macOS", "hardware_concurrency": 8, "device_memory": null, "webgl": {"vendor": "Mozilla", "renderer": "Mozilla Firefox"}, "canvas_fingerprint": "a2a5626ad1b33d94", "audio_fingerprint": "5e0edeea6a5b58aa", "fonts": ["Helvetica", "Monaco", "Segoe UI", "Tahoma", "Lucida Sans Unicode", "Menlo", "Cambria", "Verdana", "SF Pro Display", "Trebuchet MS", "Courier New", "Times New Roman", "Calibri", "Avenir", "Arial Narrow", "Arial", "Georgia", "Palatino Linotype", "Impact", "Apple Chancery", "Helvetica Neue", "Avenir Next"], "plugins": [{"name": "PDF.js", "filename": "pdf.js", "description": "Portable Document Format"}], "do_not_track": "1", "cookie_enabled": true, "online": true, "java_enabled": false, "pdf_viewer_enabled": true, "browserforge_generated": true}',0,NULL,NULL,'active',NULL,NULL,'2025-07-28 07:32:32','2025-07-28 07:34:02',4,0,NULL,1,NULL,0,NULL,NULL,1,NULL,NULL);
INSERT INTO profiles VALUES(7,'Profile_3_1753688074292','/Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Profile_3_1753688074292_5012c4a0','no_proxy',NULL,NULL,NULL,NULL,'{"user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:140.0) Gecko/******** Firefox/140.0", "viewport": {"width": 1363, "height": 783}, "screen": {"width": 1440, "height": 900, "color_depth": 30, "pixel_depth": 30, "availWidth": 1440, "availHeight": 875}, "language": "en-US", "languages": ["en-US"], "timezone": "Asia/Shanghai", "platform": "MacIntel", "os": "macOS", "hardware_concurrency": 12, "device_memory": null, "webgl": {"vendor": "Mozilla", "renderer": "Mozilla Firefox"}, "canvas_fingerprint": "5c13027ab6b2a0bc", "audio_fingerprint": "c8b8540ab0ef0dc8", "fonts": ["Microsoft Sans Serif", "Tahoma", "Helvetica Neue", "Courier New", "Apple Color Emoji", "Apple SD Gothic Neo", "Georgia", "Avenir", "Impact", "Lucida Console", "Lucida Sans Unicode", "Arial Narrow", "Verdana", "Menlo", "Times New Roman", "Segoe UI", "Monaco", "Avenir Next", "Arial Black", "Calibri", "SF Pro Display", "Apple Chancery", "Helvetica"], "plugins": [{"name": "PDF.js", "filename": "pdf.js", "description": "Portable Document Format"}], "do_not_track": "1", "cookie_enabled": true, "online": true, "java_enabled": false, "pdf_viewer_enabled": true, "browserforge_generated": true}',0,NULL,NULL,'active',NULL,NULL,'2025-07-28 07:34:34','2025-07-28 07:34:35',3,0,NULL,1,NULL,0,NULL,NULL,1,NULL,NULL);
INSERT INTO profiles VALUES(8,'test@example.com_5_1753689167800','/Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test@example.com_5_1753689167800_b067ea76','no_proxy',NULL,NULL,NULL,NULL,'{"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0", "viewport": {"width": 1840, "height": 898}, "screen": {"width": 1920, "height": 1080, "color_depth": 24, "pixel_depth": 24, "availWidth": 1920, "availHeight": 1080}, "language": "en-US", "languages": ["en-US"], "timezone": "Europe/London", "platform": "Win32", "os": "Windows", "hardware_concurrency": 20, "device_memory": null, "webgl": {"vendor": "Mozilla", "renderer": "Mozilla Firefox"}, "canvas_fingerprint": "e0b0868b731e94e4", "audio_fingerprint": "ed6066798d1a8742", "fonts": ["Times New Roman", "Arial Narrow", "Palatino Linotype", "Arial Black", "Calibri", "Lucida Sans Unicode", "Courier New", "Lucida Console", "Microsoft Sans Serif", "Arial", "Tahoma", "Verdana", "Georgia", "Trebuchet MS", "Comic Sans MS", "Impact", "Cambria", "Segoe UI"], "plugins": [{"name": "PDF.js", "filename": "pdf.js", "description": "Portable Document Format"}], "do_not_track": "1", "cookie_enabled": true, "online": true, "java_enabled": false, "pdf_viewer_enabled": true, "browserforge_generated": true}',0,NULL,NULL,'active',NULL,NULL,'2025-07-28 07:52:47','2025-07-28 07:52:49',5,0,NULL,1,NULL,0,NULL,NULL,1,NULL,NULL);
INSERT INTO profiles VALUES(9,'Profile_6_1753689508409','/Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Profile_6_1753689508409_19370e42','no_proxy',NULL,NULL,NULL,NULL,'{"user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:140.0) Gecko/******** Firefox/140.0", "viewport": {"width": 1866, "height": 923}, "screen": {"width": 1920, "height": 1080, "color_depth": 30, "pixel_depth": 30, "availWidth": 1920, "availHeight": 1055}, "language": "en-US", "languages": ["en-US"], "timezone": "Australia/Sydney", "platform": "MacIntel", "os": "macOS", "hardware_concurrency": 12, "device_memory": null, "webgl": {"vendor": "Mozilla", "renderer": "Mozilla Firefox"}, "canvas_fingerprint": "7f6a08dc8addb093", "audio_fingerprint": "1deb166f6abf0604", "fonts": ["Arial", "Palatino Linotype", "Tahoma", "Avenir", "Lucida Sans Unicode", "Courier New", "Helvetica Neue", "SF Pro Display", "Lucida Console", "Apple Color Emoji", "Impact", "Segoe UI", "Georgia", "Trebuchet MS", "Comic Sans MS", "Helvetica", "Apple SD Gothic Neo", "Microsoft Sans Serif", "Verdana", "Menlo", "San Francisco", "Times New Roman", "Monaco", "Avenir Next", "Apple Chancery", "Arial Black", "Cambria", "Arial Narrow"], "plugins": [{"name": "PDF.js", "filename": "pdf.js", "description": "Portable Document Format"}], "do_not_track": "1", "cookie_enabled": true, "online": true, "java_enabled": false, "pdf_viewer_enabled": true, "browserforge_generated": true}',0,NULL,NULL,'active',NULL,NULL,'2025-07-28 07:58:28','2025-07-28 09:08:28',6,0,NULL,1,NULL,0,NULL,NULL,1,NULL,NULL);
INSERT INTO profiles VALUES(10,'Profile_7_1753689997777','/Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Profile_7_1753689997777_51b44d5e','no_proxy',NULL,NULL,NULL,NULL,'{"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0", "viewport": {"width": 1854, "height": 948}, "screen": {"width": 1920, "height": 1080, "color_depth": 24, "pixel_depth": 24, "availWidth": 1920, "availHeight": 1032}, "language": "en-US", "languages": ["en-US"], "timezone": "Asia/Seoul", "platform": "Win32", "os": "Windows", "hardware_concurrency": 8, "device_memory": null, "webgl": {"vendor": "Mozilla", "renderer": "Mozilla Firefox"}, "canvas_fingerprint": "b4aabea3f254b2f1", "audio_fingerprint": "e63d43fe0223c554", "fonts": ["Verdana", "Comic Sans MS", "Impact", "Lucida Sans Unicode", "Arial Black", "Georgia", "Microsoft Sans Serif", "Courier New", "Palatino Linotype", "Arial", "Trebuchet MS", "Lucida Console", "Segoe UI", "Calibri", "Times New Roman", "Tahoma", "Cambria", "Arial Narrow"], "plugins": [{"name": "PDF.js", "filename": "pdf.js", "description": "Portable Document Format"}], "do_not_track": "1", "cookie_enabled": true, "online": true, "java_enabled": false, "pdf_viewer_enabled": true, "browserforge_generated": true}',0,NULL,NULL,'active',NULL,NULL,'2025-07-28 08:06:37','2025-07-28 08:06:38',7,0,NULL,1,NULL,0,NULL,NULL,1,NULL,NULL);
INSERT INTO profiles VALUES(11,'Test_Profile_1753690852','/tmp/test_profiles/Test_Profile_1753690852_0810532a','no_proxy',NULL,NULL,NULL,NULL,'null',0,NULL,'null','active',NULL,NULL,'2025-07-28 08:20:52.231645','2025-07-28 08:20:52.231645',NULL,0,NULL,1,NULL,0,NULL,NULL,0,NULL,NULL);
INSERT INTO profiles VALUES(12,'Test_Profile_1753690906','/tmp/test_profiles/Test_Profile_1753690906_a4614245','no_proxy',NULL,NULL,NULL,NULL,'null',0,NULL,'null','active',NULL,NULL,'2025-07-28 08:21:46.509517','2025-07-28 08:21:46.509517',NULL,0,NULL,1,NULL,0,NULL,NULL,0,NULL,NULL);
INSERT INTO profiles VALUES(13,'Test_Profile_1753690907','/tmp/test_profiles/Test_Profile_1753690907_0715f27e','no_proxy',NULL,NULL,NULL,NULL,'null',0,NULL,'null','active',NULL,NULL,'2025-07-28 08:21:47.579011','2025-07-28 08:21:47.579011',NULL,0,NULL,1,NULL,0,NULL,NULL,0,NULL,NULL);
INSERT INTO profiles VALUES(14,'Test_Profile_1753690908','/tmp/test_profiles/Test_Profile_1753690908_7652ce3f','no_proxy',NULL,NULL,NULL,NULL,'null',0,NULL,'null','active',NULL,NULL,'2025-07-28 08:21:48.635463','2025-07-28 08:21:48.635465',NULL,0,NULL,1,NULL,0,NULL,NULL,0,NULL,NULL);
INSERT INTO profiles VALUES(15,'Profile_1_1753691046576','/Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Profile_1_1753691046576_65ff43e3','no_proxy',NULL,NULL,NULL,NULL,'{"user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:140.0) Gecko/******** Firefox/140.0", "viewport": {"width": 1359, "height": 701}, "screen": {"width": 1440, "height": 900, "color_depth": 30, "pixel_depth": 30, "availWidth": 1440, "availHeight": 875}, "language": "en-US", "languages": ["en-US"], "timezone": "Australia/Sydney", "platform": "MacIntel", "os": "macOS", "hardware_concurrency": 10, "device_memory": null, "webgl": {"vendor": "Mozilla", "renderer": "Mozilla Firefox"}, "canvas_fingerprint": "b6f14848df3778ac", "audio_fingerprint": "115fc36a4ccda0d0", "fonts": ["Times New Roman", "Avenir Next", "Trebuchet MS", "Apple Color Emoji", "Georgia", "Calibri", "Cambria", "Arial", "Tahoma", "SF Pro Display", "Menlo", "Arial Narrow", "Comic Sans MS", "Lucida Console", "Impact", "Verdana", "Helvetica Neue", "Helvetica", "Monaco", "Apple SD Gothic Neo"], "plugins": [{"name": "PDF.js", "filename": "pdf.js", "description": "Portable Document Format"}], "do_not_track": "1", "cookie_enabled": true, "online": true, "java_enabled": false, "pdf_viewer_enabled": true, "browserforge_generated": true}',0,NULL,NULL,'active',NULL,NULL,'2025-07-28 08:24:06','2025-07-28 08:24:07',1,0,NULL,1,NULL,0,NULL,NULL,1,NULL,NULL);
INSERT INTO profiles VALUES(16,'Test_Profile_1753691581','/tmp/test_profiles/Test_Profile_1753691581_c918eb65','no_proxy',NULL,NULL,NULL,NULL,'null',0,NULL,'null','active',NULL,NULL,'2025-07-28 08:33:01.759260','2025-07-28 08:33:01.759261',NULL,0,NULL,1,NULL,0,NULL,NULL,0,NULL,NULL);
INSERT INTO profiles VALUES(17,'Test_Profile_1753691640','/tmp/test_profiles/Test_Profile_1753691640_bd44a7c0','no_proxy',NULL,NULL,NULL,NULL,'null',0,NULL,'null','active',NULL,NULL,'2025-07-28 08:34:00.981062','2025-07-28 08:34:00.981064',NULL,0,NULL,1,NULL,0,NULL,NULL,0,NULL,NULL);
INSERT INTO profiles VALUES(18,'Test_Profile_1753691642','/tmp/test_profiles/Test_Profile_1753691642_2b622f46','no_proxy',NULL,NULL,NULL,NULL,'null',0,NULL,'null','active',NULL,NULL,'2025-07-28 08:34:02.035738','2025-07-28 08:34:02.035738',NULL,0,NULL,1,NULL,0,NULL,NULL,0,NULL,NULL);
INSERT INTO profiles VALUES(19,'Test_Profile_1753691843','/tmp/test_profiles/Test_Profile_1753691843_1de312b2','no_proxy',NULL,NULL,NULL,NULL,'null',0,NULL,'null','active',NULL,NULL,'2025-07-28 08:37:23.277156','2025-07-28 08:37:23.277156',NULL,0,NULL,1,NULL,0,NULL,NULL,0,NULL,NULL);
INSERT INTO profiles VALUES(20,'Test_Profile_1753692806','/tmp/test_profiles/Test_Profile_1753692806_cbbeb580','no_proxy',NULL,NULL,NULL,NULL,'null',0,NULL,'null','active',NULL,NULL,'2025-07-28 08:53:26.243009','2025-07-28 08:53:26.243009',NULL,0,NULL,1,NULL,0,NULL,NULL,0,NULL,NULL);
INSERT INTO profiles VALUES(21,'Test_Profile_1753692907','/tmp/test_profiles/Test_Profile_1753692907_ff8b0f78','no_proxy',NULL,NULL,NULL,NULL,'null',0,NULL,'null','active',NULL,NULL,'2025-07-28 08:55:07.582070','2025-07-28 08:55:07.582071',NULL,0,NULL,1,NULL,0,NULL,NULL,0,NULL,NULL);
INSERT INTO profiles VALUES(22,'Test_Profile_1753692908','/tmp/test_profiles/Test_Profile_1753692908_cf6b0453','no_proxy',NULL,NULL,NULL,NULL,'null',0,NULL,'null','active',NULL,NULL,'2025-07-28 08:55:08.351992','2025-07-28 08:55:08.351993',NULL,0,NULL,1,NULL,0,NULL,NULL,0,NULL,NULL);
INSERT INTO profiles VALUES(23,'Test_Profile_1753692909','/tmp/test_profiles/Test_Profile_1753692909_aac26c19','no_proxy',NULL,NULL,NULL,NULL,'null',0,NULL,'null','active',NULL,NULL,'2025-07-28 08:55:09.094680','2025-07-28 08:55:09.094681',NULL,0,NULL,1,NULL,0,NULL,NULL,0,NULL,NULL);
INSERT INTO profiles VALUES(24,'Test_Profile_1753692910','/tmp/test_profiles/Test_Profile_1753692910_df577250','no_proxy',NULL,NULL,NULL,NULL,'null',0,NULL,'null','active',NULL,NULL,'2025-07-28 08:55:10.382293','2025-07-28 08:55:10.382294',NULL,0,NULL,1,NULL,0,NULL,NULL,0,NULL,NULL);
INSERT INTO profiles VALUES(25,'Test_Profile_1753693143','/tmp/test_profiles/Test_Profile_1753693143_e1c7e054','no_proxy',NULL,NULL,NULL,NULL,'null',0,NULL,'null','active',NULL,NULL,'2025-07-28 08:59:03.892933','2025-07-28 08:59:03.892934',NULL,0,NULL,1,NULL,0,NULL,NULL,0,NULL,NULL);
INSERT INTO profiles VALUES(26,'Test_Profile_1753693332','/tmp/test_profiles/Test_Profile_1753693332_b0b882c5','no_proxy',NULL,NULL,NULL,NULL,'null',0,NULL,'null','active',NULL,NULL,'2025-07-28 09:02:12.910440','2025-07-28 09:02:12.910441',NULL,0,NULL,1,NULL,0,NULL,NULL,0,NULL,NULL);
INSERT INTO profiles VALUES(27,'Test_Profile_1753693429','/tmp/test_profiles/Test_Profile_1753693429_8af901e2','no_proxy',NULL,NULL,NULL,NULL,'null',0,NULL,'null','active',NULL,NULL,'2025-07-28 09:03:49.303405','2025-07-28 09:03:49.303406',NULL,0,NULL,1,NULL,0,NULL,NULL,0,NULL,NULL);
INSERT INTO profiles VALUES(28,'Test_Profile_1753693430','/tmp/test_profiles/Test_Profile_1753693430_65232ef0','no_proxy',NULL,NULL,NULL,NULL,'null',0,NULL,'null','active',NULL,NULL,'2025-07-28 09:03:50.381053','2025-07-28 09:03:50.381053',NULL,0,NULL,1,NULL,0,NULL,NULL,0,NULL,NULL);
INSERT INTO profiles VALUES(29,'Test_Profile_1753693431','/tmp/test_profiles/Test_Profile_1753693431_b3bba0c1','no_proxy',NULL,NULL,NULL,NULL,'null',0,NULL,'null','active',NULL,NULL,'2025-07-28 09:03:51.468096','2025-07-28 09:03:51.468098',NULL,0,NULL,1,NULL,0,NULL,NULL,0,NULL,NULL);
INSERT INTO profiles VALUES(31,'Test_Profile_31_Manual','/tmp/test_profiles/Test_Profile_31_Manual','no_proxy',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'active',NULL,NULL,'2025-07-28 09:22:26','2025-07-28 09:22:26',NULL,NULL,NULL,NULL,NULL,0,NULL,NULL,0,NULL,NULL);
CREATE TABLE messaging_tasks (
	id INTEGER NOT NULL, 
	task_id VARCHAR(255) NOT NULL, 
	name VARCHAR(255) NOT NULL, 
	sender_profile_ids JSON NOT NULL, 
	recipient_list_file VARCHAR(500), 
	message_template TEXT NOT NULL, 
	message_type VARCHAR(50), 
	image_paths JSON, 
	concurrent_threads INTEGER, 
	messages_per_account_min INTEGER, 
	messages_per_account_max INTEGER, 
	delay_between_messages_min INTEGER, 
	delay_between_messages_max INTEGER, 
	avoid_duplicate_uids BOOLEAN, 
	randomize_message BOOLEAN, 
	status VARCHAR(50), 
	progress FLOAT, 
	total_recipients INTEGER, 
	messages_sent INTEGER, 
	messages_failed INTEGER, 
	messages_skipped INTEGER, 
	error_message TEXT, 
	created_at DATETIME DEFAULT CURRENT_TIMESTAMP, 
	started_at DATETIME, 
	completed_at DATETIME, 
	updated_at DATETIME DEFAULT CURRENT_TIMESTAMP, 
	PRIMARY KEY (id)
);
CREATE TABLE recipient_lists (
	id INTEGER NOT NULL, 
	list_name VARCHAR(255) NOT NULL, 
	file_path VARCHAR(500) NOT NULL, 
	total_recipients INTEGER, 
	source_task_id VARCHAR(255), 
	extra_data JSON, 
	created_at DATETIME DEFAULT CURRENT_TIMESTAMP, 
	updated_at DATETIME DEFAULT CURRENT_TIMESTAMP, 
	PRIMARY KEY (id)
);
CREATE TABLE system_logs (
	id INTEGER NOT NULL, 
	level VARCHAR(50) NOT NULL, 
	message TEXT NOT NULL, 
	module VARCHAR(255), 
	function VARCHAR(255), 
	line_number INTEGER, 
	user_id VARCHAR(255), 
	session_id VARCHAR(255), 
	task_id VARCHAR(255), 
	task_type VARCHAR(50), 
	extra_data JSON, 
	stack_trace TEXT, 
	created_at DATETIME DEFAULT CURRENT_TIMESTAMP, 
	PRIMARY KEY (id)
);
CREATE TABLE system_stats (
	id INTEGER NOT NULL, 
	cpu_usage FLOAT, 
	memory_usage FLOAT, 
	disk_usage FLOAT, 
	active_profiles INTEGER, 
	running_tasks INTEGER, 
	total_scraped_users INTEGER, 
	total_messages_sent INTEGER, 
	active_browsers INTEGER, 
	browser_memory_usage FLOAT, 
	database_size FLOAT, 
	total_records INTEGER, 
	recorded_at DATETIME DEFAULT CURRENT_TIMESTAMP, 
	PRIMARY KEY (id)
);
CREATE TABLE app_settings (
	id INTEGER NOT NULL, 
	"key" VARCHAR(255) NOT NULL, 
	value JSON NOT NULL, 
	description TEXT, 
	category VARCHAR(100), 
	is_system BOOLEAN, 
	requires_restart BOOLEAN, 
	created_at DATETIME DEFAULT CURRENT_TIMESTAMP, 
	updated_at DATETIME DEFAULT CURRENT_TIMESTAMP, 
	PRIMARY KEY (id)
);
CREATE TABLE scraping_tasks (
	id INTEGER NOT NULL, 
	task_id VARCHAR(255) NOT NULL, 
	profile_id INTEGER NOT NULL, 
	target_url VARCHAR(1000) NOT NULL, 
	scraping_types JSON NOT NULL, 
	max_results INTEGER, 
	status VARCHAR(50), 
	progress FLOAT, 
	total_found INTEGER, 
	total_scraped INTEGER, 
	results_file_path VARCHAR(500), 
	error_message TEXT, 
	created_at DATETIME DEFAULT CURRENT_TIMESTAMP, 
	started_at DATETIME, 
	completed_at DATETIME, 
	updated_at DATETIME DEFAULT CURRENT_TIMESTAMP, 
	PRIMARY KEY (id), 
	FOREIGN KEY(profile_id) REFERENCES profiles (id)
);
/****** CORRUPTION ERROR *******/
INSERT INTO scraping_tasks VALUES(44,'04a76bfc-0468-4bde-8723-9166b581192c',3,'https://www.facebook.com/groups/comailo/posts/608766675590683/','["all"]',1000,'completed',100.0,32,32,NULL,NULL,'2025-07-12 16:52:13','2025-07-12 23:52:13.637551','2025-07-12 23:52:58.060902','2025-07-12 16:52:58');
INSERT INTO scraping_tasks VALUES(43,'30f43a6c-09d6-4602-93d7-c801a0c78452',3,'https://www.facebook.com/groups/comailo/posts/608766675590683/','["all"]',1000,'completed',100.0,13,13,NULL,NULL,'2025-07-12 16:42:04','2025-07-12 23:42:04.339941','2025-07-12 23:42:39.634421','2025-07-12 16:42:39');
INSERT INTO scraping_tasks VALUES(42,'07625217-ba83-4e53-9c04-cc6768e60e0d',3,'https://www.facebook.com/groups/comailo/posts/624072560726761/','["all"]',1000,'completed',100.0,1,1,NULL,NULL,'2025-07-12 16:35:56','2025-07-12 23:35:56.901283','2025-07-12 23:36:32.412023','2025-07-12 16:36:32');
INSERT INTO scraping_tasks VALUES(41,'cc4e9d3e-0933-4bb1-a2bd-730633d8cc5b',3,'https://www.facebook.com/groups/comailo/posts/619541837846500/','["all"]',1000,'completed',100.0,34,34,NULL,NULL,'2025-07-12 16:33:12','2025-07-12 23:33:12.048609','2025-07-12 23:33:59.944345','2025-07-12 16:33:59');
INSERT INTO scraping_tasks VALUES(40,'f9d64135-e0d2-4dd3-98fc-fa14fa2933d9',3,'https://www.facebook.com/groups/comailo/posts/619541837846500/','["all"]',1000,'completed',100.0,34,34,NULL,NULL,'2025-07-12 16:24:16','2025-07-12 23:24:16.269648','2025-07-12 23:25:45.474427','2025-07-12 16:25:45');
INSERT INTO scraping_tasks VALUES(39,'f8e09200-a9ee-49be-aa60-2ebd2687d2b2',3,'https://www.facebook.com/groups/nghiengoogleviet/posts/2389761474790384','["all"]',1000,'completed',100.0,32,32,NULL,NULL,'2025-07-12 16:12:25','2025-07-12 23:12:25.726246','2025-07-12 23:14:08.469511','2025-07-12 16:14:08');
CREATE TABLE message_logs (
	id INTEGER NOT NULL, 
	task_id INTEGER NOT NULL, 
	sender_profile_id INTEGER NOT NULL, 
	recipient_uid VARCHAR(255) NOT NULL, 
	recipient_name VARCHAR(500), 
	recipient_profile_url VARCHAR(1000), 
	message_content TEXT NOT NULL, 
	message_type VARCHAR(50) NOT NULL, 
	image_paths JSON, 
	status VARCHAR(50), 
	error_message TEXT, 
	response_data JSON, 
	created_at DATETIME DEFAULT CURRENT_TIMESTAMP, 
	sent_at DATETIME, 
	PRIMARY KEY (id), 
	FOREIGN KEY(task_id) REFERENCES messaging_tasks (id), 
	FOREIGN KEY(sender_profile_id) REFERENCES profiles (id)
);
CREATE TABLE bulk_messaging_statistics (
	id INTEGER NOT NULL, 
	task_id VARCHAR NOT NULL, 
	task_name VARCHAR NOT NULL, 
	sender_profile_id INTEGER NOT NULL, 
	status VARCHAR NOT NULL, 
	total_recipients INTEGER NOT NULL, 
	messages_sent INTEGER NOT NULL, 
	messages_failed INTEGER NOT NULL, 
	messages_skipped INTEGER NOT NULL, 
	success_rate FLOAT NOT NULL, 
	total_time_seconds FLOAT NOT NULL, 
	total_time_formatted VARCHAR, 
	average_time_per_message FLOAT NOT NULL, 
	browser_closed BOOLEAN NOT NULL, 
	error_message TEXT, 
	created_at DATETIME NOT NULL, 
	started_at DATETIME, 
	completed_at DATETIME, 
	detailed_results JSON, 
	PRIMARY KEY (id), 
	FOREIGN KEY(sender_profile_id) REFERENCES profiles (id)
);
CREATE TABLE scraped_users (
	id INTEGER NOT NULL, 
	task_id INTEGER NOT NULL, 
	facebook_uid VARCHAR(255) NOT NULL, 
	full_name VARCHAR(500), 
	profile_url VARCHAR(1000), 
	gender VARCHAR(50), 
	profile_picture_url VARCHAR(1000), 
	interaction_type VARCHAR(50) NOT NULL, 
	interaction_content TEXT, 
	interaction_timestamp DATETIME, 
	extra_data JSON, 
	scraped_at DATETIME DEFAULT CURRENT_TIMESTAMP, 
	PRIMARY KEY (id), 
	FOREIGN KEY(task_id) REFERENCES scraping_tasks (id)
);
CREATE TABLE profile_local_storage (
	id INTEGER NOT NULL, 
	profile_id INTEGER NOT NULL, 
	domain VARCHAR(255) NOT NULL, 
	"key" VARCHAR(255) NOT NULL, 
	value TEXT, 
	data_type VARCHAR(50), 
	is_compressed BOOLEAN, 
	compression_type VARCHAR(50), 
	encoding VARCHAR(50), 
	original_size INTEGER, 
	compressed_size INTEGER, 
	sync_version INTEGER, 
	last_modified DATETIME DEFAULT CURRENT_TIMESTAMP, 
	checksum VARCHAR(64), 
	sync_status VARCHAR(50), 
	created_at DATETIME DEFAULT CURRENT_TIMESTAMP, 
	updated_at DATETIME DEFAULT CURRENT_TIMESTAMP, 
	PRIMARY KEY (id), 
	FOREIGN KEY(profile_id) REFERENCES profiles (id)
);
CREATE TABLE profile_indexeddb (
	id INTEGER NOT NULL, 
	profile_id INTEGER NOT NULL, 
	domain VARCHAR(255) NOT NULL, 
	database_name VARCHAR(255) NOT NULL, 
	object_store VARCHAR(255) NOT NULL, 
	"key" VARCHAR(255) NOT NULL, 
	value BLOB, 
	value_type VARCHAR(50) NOT NULL, 
	is_compressed BOOLEAN, 
	compression_type VARCHAR(50), 
	original_size INTEGER, 
	compressed_size INTEGER, 
	schema_version INTEGER, 
	sync_version INTEGER, 
	last_modified DATETIME DEFAULT CURRENT_TIMESTAMP, 
	checksum VARCHAR(64), 
	sync_status VARCHAR(50), 
	created_at DATETIME DEFAULT CURRENT_TIMESTAMP, 
	updated_at DATETIME DEFAULT CURRENT_TIMESTAMP, 
	PRIMARY KEY (id), 
	FOREIGN KEY(profile_id) REFERENCES profiles (id)
);
CREATE TABLE profile_browser_history (
	id INTEGER NOT NULL, 
	profile_id INTEGER NOT NULL, 
	url TEXT NOT NULL, 
	title TEXT, 
	visit_time DATETIME NOT NULL, 
	visit_count INTEGER, 
	typed_count INTEGER, 
	transition_type VARCHAR(50), 
	referrer_url TEXT, 
	favicon_url TEXT, 
	page_metadata JSON, 
	sync_version INTEGER, 
	last_modified DATETIME DEFAULT CURRENT_TIMESTAMP, 
	sync_status VARCHAR(50), 
	created_at DATETIME DEFAULT CURRENT_TIMESTAMP, 
	updated_at DATETIME DEFAULT CURRENT_TIMESTAMP, 
	PRIMARY KEY (id), 
	FOREIGN KEY(profile_id) REFERENCES profiles (id)
);
CREATE TABLE profile_configurations (
	id INTEGER NOT NULL, 
	profile_id INTEGER NOT NULL, 
	preferences JSON, 
	extensions JSON, 
	bookmarks JSON, 
	certificates JSON, 
	security_settings JSON, 
	autofill_data JSON, 
	sync_version INTEGER, 
	last_modified DATETIME DEFAULT CURRENT_TIMESTAMP, 
	checksum VARCHAR(64), 
	sync_status VARCHAR(50), 
	created_at DATETIME DEFAULT CURRENT_TIMESTAMP, 
	updated_at DATETIME DEFAULT CURRENT_TIMESTAMP, 
	PRIMARY KEY (id), 
	FOREIGN KEY(profile_id) REFERENCES profiles (id)
);
CREATE TABLE profile_sync_logs (
	id INTEGER NOT NULL, 
	profile_id INTEGER NOT NULL, 
	sync_type VARCHAR(50) NOT NULL, 
	sync_direction VARCHAR(50) NOT NULL, 
	started_at DATETIME NOT NULL, 
	completed_at DATETIME, 
	status VARCHAR(50) NOT NULL, 
	items_synced INTEGER, 
	data_size_bytes BIGINT, 
	conflicts_resolved INTEGER, 
	error_message TEXT, 
	error_details JSON, 
	initiated_by_user_id INTEGER, 
	user_ip_address VARCHAR(45), 
	created_at DATETIME DEFAULT CURRENT_TIMESTAMP, 
	PRIMARY KEY (id), 
	FOREIGN KEY(profile_id) REFERENCES profiles (id)
);
CREATE INDEX ix_profiles_id ON profiles (id);
CREATE UNIQUE INDEX ix_profiles_name ON profiles (name);
CREATE UNIQUE INDEX ix_messaging_tasks_task_id ON messaging_tasks (task_id);
CREATE INDEX ix_messaging_tasks_id ON messaging_tasks (id);
CREATE INDEX ix_recipient_lists_id ON recipient_lists (id);
CREATE INDEX ix_system_logs_id ON system_logs (id);
CREATE INDEX ix_system_logs_level ON system_logs (level);
CREATE INDEX ix_system_logs_created_at ON system_logs (created_at);
CREATE INDEX ix_system_stats_recorded_at ON system_stats (recorded_at);
CREATE INDEX ix_system_stats_id ON system_stats (id);
CREATE UNIQUE INDEX ix_app_settings_key ON app_settings ("key");
CREATE INDEX ix_app_settings_id ON app_settings (id);
CREATE INDEX ix_scraping_tasks_id ON scraping_tasks (id);
CREATE UNIQUE INDEX ix_scraping_tasks_task_id ON scraping_tasks (task_id);
CREATE INDEX ix_message_logs_id ON message_logs (id);
CREATE INDEX ix_message_logs_recipient_uid ON message_logs (recipient_uid);
CREATE UNIQUE INDEX ix_bulk_messaging_statistics_task_id ON bulk_messaging_statistics (task_id);
CREATE INDEX ix_bulk_messaging_statistics_id ON bulk_messaging_statistics (id);
CREATE INDEX ix_profile_local_storage_id ON profile_local_storage (id);
CREATE INDEX idx_domain_modified ON profile_local_storage (domain, last_modified);
CREATE INDEX idx_profile_sync_status ON profile_local_storage (profile_id, sync_status);
CREATE INDEX ix_profile_local_storage_profile_id ON profile_local_storage (profile_id);
CREATE UNIQUE INDEX idx_profile_domain_key ON profile_local_storage (profile_id, domain, "key");
CREATE INDEX ix_profile_local_storage_domain ON profile_local_storage (domain);
CREATE INDEX ix_profile_indexeddb_profile_id ON profile_indexeddb (profile_id);
CREATE INDEX ix_profile_indexeddb_id ON profile_indexeddb (id);
CREATE INDEX ix_profile_indexeddb_domain ON profile_indexeddb (domain);
CREATE INDEX idx_indexeddb_size ON profile_indexeddb (original_size, compressed_size);
CREATE INDEX idx_profile_indexeddb_sync ON profile_indexeddb (profile_id, sync_status);
CREATE UNIQUE INDEX idx_profile_indexeddb ON profile_indexeddb (profile_id, domain, database_name, object_store, "key");
CREATE INDEX idx_profile_history_sync ON profile_browser_history (profile_id, sync_status);
CREATE INDEX idx_profile_visit_time ON profile_browser_history (profile_id, visit_time);
CREATE INDEX idx_profile_url_time ON profile_browser_history (profile_id, url, visit_time);
CREATE INDEX ix_profile_browser_history_profile_id ON profile_browser_history (profile_id);
CREATE INDEX ix_profile_browser_history_id ON profile_browser_history (id);
CREATE INDEX ix_profile_configurations_profile_id ON profile_configurations (profile_id);
CREATE INDEX ix_profile_configurations_id ON profile_configurations (id);
CREATE INDEX idx_profile_config_sync ON profile_configurations (profile_id, sync_status);
CREATE INDEX idx_sync_user ON profile_sync_logs (initiated_by_user_id, started_at);
CREATE INDEX ix_profile_sync_logs_profile_id ON profile_sync_logs (profile_id);
CREATE INDEX ix_profile_sync_logs_id ON profile_sync_logs (id);
ROLLBACK; -- due to errors
