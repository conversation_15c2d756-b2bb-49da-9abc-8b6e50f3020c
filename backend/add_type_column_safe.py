#!/usr/bin/env python3
"""
Safe script to add type column to profiles table
"""

import sqlite3
import os
import sys

def add_type_column_safe():
    """Add type column to profiles table safely"""
    
    # Find database file
    possible_paths = [
        'facebook_automation.db',
        'data/facebook_automation.db',
        '../facebook_automation.db'
    ]
    
    db_path = None
    for path in possible_paths:
        if os.path.exists(path):
            db_path = path
            break
    
    if not db_path:
        print("❌ Database not found")
        return False
    
    print(f"📊 Found database at: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if profiles table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='profiles'")
        if not cursor.fetchone():
            print("❌ Profiles table not found")
            conn.close()
            return False
        
        # Check if type column exists
        cursor.execute('PRAGMA table_info(profiles)')
        columns = [column[1] for column in cursor.fetchall()]
        print(f"📋 Current columns: {columns}")
        
        if 'type' not in columns:
            print('🔧 Adding type column to profiles table...')
            
            # Add column as nullable first
            cursor.execute('ALTER TABLE profiles ADD COLUMN type VARCHAR(50)')
            
            # Update existing records to have default value
            cursor.execute('UPDATE profiles SET type = "facebook" WHERE type IS NULL')
            
            # Create index
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_profiles_type ON profiles (type)')
            
            conn.commit()
            print('✅ Type column added successfully')
            
            # Verify
            cursor.execute('PRAGMA table_info(profiles)')
            new_columns = [column[1] for column in cursor.fetchall()]
            print(f"📋 New columns: {new_columns}")
            
            # Show sample data
            cursor.execute('SELECT id, name, type FROM profiles LIMIT 3')
            profiles = cursor.fetchall()
            if profiles:
                print("📊 Sample profiles:")
                for profile in profiles:
                    print(f"   - ID: {profile[0]}, Name: {profile[1]}, Type: {profile[2]}")
            else:
                print("📊 No existing profiles found")
                
        else:
            print('✅ Type column already exists')
            
            # Show sample data
            cursor.execute('SELECT id, name, type FROM profiles LIMIT 3')
            profiles = cursor.fetchall()
            if profiles:
                print("📊 Sample profiles:")
                for profile in profiles:
                    print(f"   - ID: {profile[0]}, Name: {profile[1]}, Type: {profile[2]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Adding type column to profiles table safely...")
    success = add_type_column_safe()
    if success:
        print("🎉 Operation completed successfully!")
        sys.exit(0)
    else:
        print("💥 Operation failed!")
        sys.exit(1)
